package com.beikes.anlugrid.base

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Environment
import android.util.Log
import android.widget.Toast
import androidx.core.content.FileProvider
import com.beikes.anlugrid.MyApplication
import com.beikes.anlugrid.upgradeConfig
import com.beikes.anlugrid.utils.GlobalSPUtils
import com.blankj.utilcode.util.PathUtils
import com.google.gson.Gson
import okhttp3.Call
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

data class Response(
    val data: Data
)

data class Data(
    val version: String,
    val file_url: String
)

class AutoUpdate(private val context: Context) {
    private val requestUrl =
        "https://xiaozhi.beikesmart.com/release_api/general/project/opr_project/service/anonymous/machine_api/get_update_info"
    private val payload4App = upgradeConfig
    private val contentType = "application/json; charset=utf-8".toMediaTypeOrNull()

    fun log(msg: String) {
        Log.e(MyApplication.LOG_TAG, msg)
        val client = OkHttpClient()
        val data = JSONObject().apply {
            put("sn", "Upgrade${GlobalSPUtils.getDeviceId()}")
            put("data", msg)
        }
        val request = Request.Builder()
            .url("https://xiaozhi.beikesmart.com/xiaozhi-log")
            .post(data.toString().toRequestBody("application/json".toMediaTypeOrNull()))
            .build()
        client.newCall(request).execute()
    }

    fun run() {
        val client = OkHttpClient()
        val body4App = payload4App.toRequestBody(contentType)
        val request4App = Request.Builder()
            .url(requestUrl)
            .post(body4App)
            .build()
        try {
            client.newCall(request4App).execute().use { response ->
                if (!response.isSuccessful) {
                    return log("检查更新失败")
                }

                val responseData = response.body?.string()
                val result = Gson().fromJson(responseData, Response::class.java)
                val version = getAppVersionInfo(this.context, this.context.packageName)
                log("当前${MyApplication.LOG_TAG}版本号: $version")
                if (result.data.version != version) {
                    log("启动更新,当前版本$version, 服务器最新版本${result.data.version}")
                    update(result.data.file_url)
                }
            }
        } catch (e: Exception) {
            log("更新出现异常：${e.message}")
        }
    }

    private fun getAppVersionInfo(context: Context, packageName: String): String {
        val packageManager = context.packageManager
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val versionName = packageInfo.versionName
            return versionName.toString()
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
            return ""
        }
    }

    private fun createApkFile(): File {
        val dir = context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)
        dir?.mkdirs()
        return File.createTempFile("app-", ".apk", dir)
    }

    private fun update(url: String) {
        val client = OkHttpClient()
        val to = PathUtils.getExternalAppCachePath()
        log("安装路径, 应用下载到$to, 应用来源$url")

        val request = Request.Builder()
            .url(url)
            .build()

        client.newCall(request).enqueue(object : okhttp3.Callback {
            override fun onFailure(call: Call, e: IOException) {
                Toast.makeText(context, "下载更新失败", Toast.LENGTH_SHORT).show()
            }

            override fun onResponse(call: Call, response: okhttp3.Response) {
                if (!response.isSuccessful) throw IOException("Unexpected code $response")

                val apkFile = createApkFile()
                val inputStream: InputStream? = response.body?.byteStream()
                val outputStream = FileOutputStream(apkFile)

                inputStream?.copyTo(outputStream)
                inputStream?.close()
                outputStream.close()
                log("下载完成，准备进行安装${MyApplication.LOG_TAG}，${apkFile.path}, ${apkFile.length()}")
                installApk(apkFile)
            }
        })

    }

    private fun installApk(file: File) {
        val contentUri = FileProvider.getUriForFile(
            context,
            "${context.applicationContext.packageName}.fileprovider",
            file
        )

        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(contentUri, "application/vnd.android.package-archive")
            flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_ACTIVITY_NEW_TASK
        }

        context.startActivity(intent)
    }
}