{"logs": [{"outputFile": "com.beikes.anlugrid.test.app-mergeDebugAndroidTestResources-34:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,1016,1103,1176,1250,1323,1396,1475,1543", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,73,72,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,1011,1098,1171,1245,1318,1391,1470,1538,1656"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,929,1013,1123,1223,1308,1390,1488,1577,1662,1747,1834,1907,1981,2054,2228,2307,2375", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,73,72,72,78,67,117", "endOffsets": "924,1008,1118,1218,1303,1385,1483,1572,1657,1742,1829,1902,1976,2049,2122,2302,2370,2488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,410,517,621,725,2127", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "200,302,405,512,616,720,831,2223"}}]}]}