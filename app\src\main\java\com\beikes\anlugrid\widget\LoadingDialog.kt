package com.beikes.anlugrid.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import com.beikes.anlugrid.R

/**
 * 自定义加载dialog
 */
class LoadingDialog(context: Context, isCancelable: Boolean = false) : Dialog(context) {

    init {
        setContentView(R.layout.dialog_loading)
        //先给dialog设置背景为透明 自定义背景才能生效
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.attributes?.systemUiVisibility =
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_IMMERSIVE
        val p = window?.attributes
        //设置dialog的黑暗度
        p?.dimAmount = 0.5f
        window?.attributes = p
        //设置dialog不能取消
        setCancelable(isCancelable)
    }
}