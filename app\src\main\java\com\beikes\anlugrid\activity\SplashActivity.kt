package com.beikes.anlugrid.activity

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.beikes.anlugrid.R
import com.beikes.anlugrid.base.BaseActivity
import com.beikes.anlugrid.start
import com.beikes.anlugrid.utils.GlobalSPUtils
import com.blankj.utilcode.util.ClickUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.PermissionUtils
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

/**
 * 闪屏页
 * 1、网络环境检测
 */
@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseActivity() {

    private val TAG = "SplashActivity"
    override fun setLayoutId(): Int = R.layout.activity_splash
    private lateinit var llSplash: LinearLayout
    private lateinit var tvTip: TextView
    private var networkCheckStartTime: Long = 0
    private val NETWORK_CHECK_TIMEOUT: Long = 60 * 1000 // 1 minute in milliseconds
    private var hasShownDialog = false

    override fun init(savedInstanceState: Bundle?) {
        llSplash = findViewById(R.id.llSplash)
        tvTip = findViewById(R.id.tvTip)
        initPermission()
//        initListener()
    }

    private fun next() {
        val url = GlobalSPUtils.getUrl()
        if (url.isEmpty()) {
            showInputUrlDialog()
        } else {
            startNetworkListener(3)
        }
    }

    private fun initPermission() {
        PermissionUtils.permission(
            Manifest.permission_group.CAMERA,
            Manifest.permission.RECORD_AUDIO
        ).callback(object : PermissionUtils.FullCallback {
            override fun onGranted(granted: MutableList<String>) {
                next()
            }

            override fun onDenied(deniedForever: MutableList<String>, denied: MutableList<String>) {

            }

        }).request()
    }

    private fun initListener() {
        llSplash.setOnClickListener(object : ClickUtils.OnMultiClickListener(2) {
            override fun onTriggerClick(v: View?) {
                LogUtils.e("多次点击了")
                showInputUrlDialog()
            }

            override fun onBeforeTriggerClick(v: View?, count: Int) {
            }
        })
    }

    private fun showInputUrlDialog() {
        stopNetworkListener()
        val builder = AlertDialog.Builder(this)
        val editText = EditText(this)
        editText.setText(GlobalSPUtils.getUrl())
        val btn = Button(this).apply {
            setOnClickListener {
                MainActivity.start(this@SplashActivity, "https://debugtbs.qq.com")
            }
        }
        btn.text = "下载内核"
        val llContent = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            addView(editText)
            addView(btn)
        }
        builder.setTitle("输入地址")
            .setView(llContent)
            .setPositiveButton("确定") { dialog, _ ->
                var input = editText.text.toString()
                if (input == "test") {
                    input = "file:///android_asset/test.html"
                }
                GlobalSPUtils.saveUrl(input)
                startNetworkListener()
                dialog.dismiss()
            }
            .setNegativeButton("取消") { dialog, _ ->
                startNetworkListener()
                dialog.dismiss()
            }
            .setCancelable(false)
            .create()
            .show()
    }


    /**
     * 通过轮训定时来监听网络状态 每2s ping一次网络
     */
    private var mCheckObservable: Disposable? = null

    @SuppressLint("CheckResult")
    private fun startNetworkListener(initialDelay: Long = 0) {
        runOnUiThread {
            tvTip.text = "正在检查网络..."
        }
        stopNetworkListener()
        networkCheckStartTime = System.currentTimeMillis()
        Observable.interval(initialDelay, 2, TimeUnit.SECONDS)
            .doOnSubscribe {
                mCheckObservable = it
            }
            .subscribeOn(Schedulers.io())
            .flatMap {
                return@flatMap Observable.just(NetworkUtils.isAvailableByPing())
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({
                //每5s ping一次网络，如果有网络则发起请求，没有则继续轮训
                Log.e(TAG, "isPing：$it")
                if (it) {
                    //检查网络更新，同时停止轮询
                    stopNetworkListener()
                    start(this, MainActivity::class.java)
                    finish()
                } else {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - networkCheckStartTime >= NETWORK_CHECK_TIMEOUT && !hasShownDialog) {
                        showNetworkErrorDialog()
                        hasShownDialog = true
                    }
                }
            }, {

            })
    }

    private fun showNetworkErrorDialog() {
        AlertDialog.Builder(this)
            .setTitle("网络错误")
            .setMessage("您的设备在一分钟内未能连接到网络。请检查您的网络设置。")
            .setPositiveButton("确定") { _, _ ->
                // 用户点击确定后退出应用程序
                finishAffinity() // 关闭所有活动并退出应用
            }
            .setCancelable(false) // 防止用户通过点击外部区域关闭对话框
            .show()
    }


    /**
     * 停止轮训
     */
    private fun stopNetworkListener() {
        if (mCheckObservable != null && !mCheckObservable!!.isDisposed) {
            mCheckObservable!!.dispose()
            mCheckObservable = null
        }

    }


    override fun onDestroy() {
        super.onDestroy()
        stopNetworkListener()
        Log.e(TAG, "onDestroy")
    }

}
