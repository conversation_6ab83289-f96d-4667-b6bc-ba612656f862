package com.beikes.anlugrid.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.location.LocationManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.telephony.TelephonyManager
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.webkit.JavascriptInterface
import android.widget.LinearLayout
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import com.beikes.anlugrid.MyApplication
import com.beikes.anlugrid.R
import com.beikes.anlugrid.base.AutoUpdate
import com.beikes.anlugrid.base.BaseActivity
import com.beikes.anlugrid.defaultAlipayInit
import com.beikes.anlugrid.defaultIsTest
import com.beikes.anlugrid.start
import com.beikes.anlugrid.utils.AlipayManager
import com.beikes.anlugrid.utils.GlobalSPUtils
import com.beikes.anlugrid.utils.runFinishAction
import com.beikes.anlugrid.utils.setFinishAction
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.GsonUtils
import com.tencent.smtt.export.external.interfaces.PermissionRequest
import com.tencent.smtt.export.external.interfaces.SslError
import com.tencent.smtt.export.external.interfaces.SslErrorHandler
import com.tencent.smtt.export.external.interfaces.WebResourceError
import com.tencent.smtt.export.external.interfaces.WebResourceRequest
import com.tencent.smtt.sdk.ValueCallback
import com.tencent.smtt.sdk.WebChromeClient
import com.tencent.smtt.sdk.WebSettings
import com.tencent.smtt.sdk.WebView
import com.tencent.smtt.sdk.WebViewClient
import io.reactivex.disposables.Disposable
import org.json.JSONObject
import java.io.File

class MainActivity : BaseActivity(), AlipayManager.FaceCall {

    private var exitTime = 0L
    private var url = ""
    private lateinit var webView: WebView
    private lateinit var llProgress: LinearLayout
    private var isTest = defaultIsTest

    //相机和文件
    private var jsCallbackId: String? = null
    private var jsCallType: String? = null
    private val REQUEST_CODE_CAMERA = 2001
    private var cameraImageUri: Uri? = null
    private var currentPhotoFileName: String? = null
    private val capturedPhotoPaths = mutableListOf<String>()
    private val REQUEST_CODE_FILE = 2002
    private var uploadFileCallback: ValueCallback<Array<Uri>>? = null

    // Baidu 定位
    private var mLocationClient: LocationClient? = null
    private var mLocationCallbackId: String? = null
    private var isLocating = false


    companion object {
        fun start(context: Context, url: String) {
            context.startActivity(Intent(context, MainActivity::class.java).apply {
                putExtra("url", url)
            })
        }
    }

    override fun setLayoutId(): Int = R.layout.activity_main

    override fun init(savedInstanceState: Bundle?) {
        webView = findViewById(R.id.webView)
        llProgress = findViewById(R.id.progress)
        val intentUrl = intent.getStringExtra("url")
        url = if (intentUrl.isNullOrEmpty()) {
            GlobalSPUtils.getUrl()
        } else {
            intentUrl
        }
        if (isTest) {
            url = "file:///android_asset/util.html"
        }
        initBaiduLocationClient()
        initWeb()
        if(defaultAlipayInit){
            MyApplication.mInstance.mAlipayManager.init();
            MyApplication.mInstance.mAlipayManager.setCall(this)
        }
        setFinishAction {
            this.finish()
        }


        Thread {
            try {
                AutoUpdate(this).run()
            } catch (e: Exception) {
                e.printStackTrace()
                e.message?.let { Log.e(MyApplication.LOG_TAG, it) }
            }
        }.start()
    }

    private fun initBaiduLocationClient() {
        LocationClient.setAgreePrivacy(true)  // 必须调用
        mLocationClient = LocationClient(applicationContext)
        val option = LocationClientOption().apply {
            locationMode = LocationClientOption.LocationMode.Hight_Accuracy
            isOpenGps = true
            setIsNeedAddress(true)
            setCoorType("bd09ll") // 百度坐标系
            scanSpan = 0          // 单次定位
            isOnceLocation = true
        }
        mLocationClient?.locOption = option
        mLocationClient?.registerLocationListener(object : BDAbstractLocationListener() {
            override fun onReceiveLocation(location: BDLocation?) {
                isLocating = false
                val callbackId = mLocationCallbackId
                mLocationCallbackId = null
                val resultMap = if (location != null && location.locType != BDLocation.TypeServerError) {
                    mapOf(
                        "latitude" to location.latitude,
                        "longitude" to location.longitude,
                        "address" to location.addrStr
                    )
                } else {
                    mapOf("error" to "定位失败")
                }
                val resultJson = GsonUtils.toJson(resultMap)
                val escapedJson = JSONObject.quote(resultJson) // 自动加双引号并转义
                val jsCode = "javascript:onNativeResult('$callbackId', JSON.parse($escapedJson))"
                webView.post {
                    webView.evaluateJavascript(jsCode, null)
                }
                // 这里不调用 stop(), 保持 client 可用，等下次调用
            }
        })
    }

    /**
     * 加载web页面
     */
    @SuppressLint("SetJavaScriptEnabled", "AddJavascriptInterface")
    private fun initWeb() {
        val webSetting = webView.settings
        webSetting.javaScriptEnabled = true
        // 缩放,设置为不能缩放可以防止页面上出现放大和缩小的图标
        webSetting.builtInZoomControls = false
        webSetting.displayZoomControls = false
        webSetting.setSupportZoom(true)
        webSetting.loadWithOverviewMode = true
        //设置此属性，可任意比例缩放
        webSetting.useWideViewPort = true
        val layerType = View.LAYER_TYPE_HARDWARE
        webView.setLayerType(layerType, null)
        val appCachePath = applicationContext.cacheDir.absolutePath
        webSetting.databasePath = appCachePath
        // 缓存
        webSetting.cacheMode = WebSettings.LOAD_DEFAULT
        // 开启DOM storage API功能
        webSetting.domStorageEnabled = true
        // 开启application Cache功能
        webSetting.setAppCacheEnabled(false)
        //支持多窗口
        webSetting.setSupportMultipleWindows(true)
        webSetting.javaScriptCanOpenWindowsAutomatically = true
        //解决图片不显示
        webSetting.blockNetworkImage = false
        webSetting.textZoom = 100
        webSetting.mediaPlaybackRequiresUserGesture = false
        webSetting.allowContentAccess = true
        webSetting.allowFileAccess = true
        webSetting.databaseEnabled = true
        webSetting.setGeolocationEnabled(true)
        webSetting.loadsImagesAutomatically = true
        webSetting.javaScriptEnabled = true
        webSetting.layoutAlgorithm =
            WebSettings.LayoutAlgorithm.SINGLE_COLUMN
        webView.isHorizontalScrollBarEnabled = true
        webView.isVerticalScrollBarEnabled = true
        webView.setInitialScale(100)
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView?, request: WebResourceRequest?
            ): Boolean {
                webView.loadUrl(url)
                return false
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                llProgress.visibility = View.VISIBLE
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                llProgress.visibility = View.GONE
            }

            override fun onReceivedError(
                view: WebView?, request: WebResourceRequest?, error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
            }

            //屏蔽ssl证书过期的错误
            override fun onReceivedSslError(
                p0: WebView?, p1: SslErrorHandler?, p2: SslError?
            ) {
                p1?.proceed()
            }

        }
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (newProgress >= 90) {
                    llProgress.visibility = View.GONE
                }
            }

            //直接授权
            override fun onPermissionRequest(p0: PermissionRequest?) {
                p0?.grant(p0.resources)
            }

            override fun onShowFileChooser(
                webView: WebView?,
                filePathCallback:ValueCallback<Array<Uri>>?,
                fileChooserParams: FileChooserParams?
            ): Boolean {
                uploadFileCallback?.onReceiveValue(null)
                uploadFileCallback = filePathCallback

                val intent = fileChooserParams?.createIntent() ?: Intent(Intent.ACTION_GET_CONTENT)
                intent.addCategory(Intent.CATEGORY_OPENABLE)
                val allowMultiple = intent?.getBooleanExtra(Intent.EXTRA_ALLOW_MULTIPLE, false) ?: false
                intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, allowMultiple)
                intent.type = "*/*"

                try {
                    startActivityForResult(intent, REQUEST_CODE_FILE)
                } catch (e: Exception) {
                    uploadFileCallback = null
                    return false
                }
                return true
            }


        }
        //注入js 实现js与原生通讯
        webView.addJavascriptInterface(JsBridge(), "android")
        webView.loadUrl(url)
    }

    override fun onDestroy() {
        webView.destroy()
        stopTimer()
        if(defaultAlipayInit){
            MyApplication.mInstance.mAlipayManager.release()
        }
        mLocationClient?.stop()
        super.onDestroy()
    }

    /**
     * 当监听到页面打开失败后 每2min重试一次
     */
    private var mCheckObservable: Disposable? = null

    /**
     * 停止轮训
     */
    private fun stopTimer() {
        if (mCheckObservable != null && !mCheckObservable!!.isDisposed) {
            mCheckObservable!!.dispose()
            mCheckObservable = null
        }
    }

    /**
     * 双击退出
     */
    @SuppressLint("SourceLockedOrientationActivity")
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (webView.canGoBack()) {
                webView.goBack()
            } else {
                exitApp()
            }
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_CODE_CAMERA) {
            // 直接使用创建时的 photoFile
            val photoFile = File(getExternalFilesDir(null), currentPhotoFileName)
            if (photoFile.exists()) {
                val filePath = photoFile.absolutePath
                val jsCode = "javascript:onNativeResult('${jsCallbackId}', '${filePath}')"
                webView.post {
                    webView.evaluateJavascript(jsCode, null)
                }
            } else {
                Log.e("camera", "照片文件不存在: ${photoFile.absolutePath}")
            }
        } else if (requestCode == REQUEST_CODE_FILE) {
            if (uploadFileCallback == null) return

            val results: Array<Uri>? = if (resultCode == Activity.RESULT_OK && data != null) {
                val clipData = data.clipData
                if (clipData != null) {
                    Array(clipData.itemCount) { i -> clipData.getItemAt(i).uri }
                } else {
                    arrayOf(data.data!!)
                }
            } else {
                null
            }
            uploadFileCallback?.onReceiveValue(results)
            uploadFileCallback = null
        }

        // 清空状态
        jsCallbackId = null
        jsCallType = null
    }



    private fun exitApp() {
        if (System.currentTimeMillis() - exitTime > 2000) {
            exitTime = System.currentTimeMillis()
        } else {
            finish()
        }
    }



    /**
     * JS与原生通讯
     */
    inner class JsBridge {

        /**
         * 人脸状态
         */
        @JavascriptInterface
        fun getFaceState(): Boolean {
            Log.e("zp", "getFaceState")
            return MyApplication.mInstance.mAlipayManager.isInit;
        }

        /**
         * 人脸识别
         */
        @JavascriptInterface
        fun getFaceInfo(): Int {
            Log.e("zp", "getFaceInfo")
            val alipayManager = MyApplication.mInstance.mAlipayManager
            if (!alipayManager.isInit) {
                return -1;
            }
            //走支付宝扫脸流程
            val isStart: Boolean = alipayManager.invokeScanFace()
            if (!isStart) {
                return -2;
            }
            return 0;
        }

        /**
         * 测试用方法，用于检测js与原生通讯是否正确
         */
        @JavascriptInterface
        fun jsToast(msg: String) {
            Toast.makeText(this@MainActivity, msg, Toast.LENGTH_SHORT).show()
        }

        /**
         * 获取设备sn码
         * 获取androidId
         */
        @JavascriptInterface
        fun getDeviceId(): String {
            Log.e("zp", "getDeviceId")
            return GlobalSPUtils.getDeviceId()
        }

        /**
         * 获取设备的一些信息
         */
        @SuppressLint("MissingPermission")
        @JavascriptInterface
        fun getDeviceInfo(): String {
            return GsonUtils.toJson(hashMapOf<String, Any>().apply {
                put("sn", getDeviceId())
                put("androidId", DeviceUtils.getAndroidID())
                put("manufacturer", DeviceUtils.getManufacturer())
                put("model", DeviceUtils.getModel())
                put("macAddress", DeviceUtils.getMacAddress())
                put("androidVersion", DeviceUtils.getSDKVersionName())
                put("versionCode", AppUtils.getAppVersionCode())
                put("versionName", AppUtils.getAppVersionName())
            })
        }

        /**
         * 页面刷新
         */
        @JavascriptInterface
        fun reload() {
            Log.e("zp", "reload: ")
            start(this@MainActivity, MainActivity::class.java)
            finish()
        }

        /**
         * 页面重新加载url
         */
        @JavascriptInterface
        fun loadUrl(newUrl: String = "") {
            if (newUrl.isEmpty()) {
                reload()
            } else {
                GlobalSPUtils.saveUrl(newUrl)
                start(this@MainActivity, MainActivity::class.java)
            }
        }

        /**
         * 推送过来的配置信息
         */
        @JavascriptInterface
        fun postClientConfig(config: String) {
            Log.e("zp", "postClientConfig: $config")
        }

        @JavascriptInterface
        fun startRecorder(): Boolean {
            return false
        }

        @JavascriptInterface
        fun stopRecorder(): Boolean {
            return false
        }

        @JavascriptInterface
        fun exit() {
            runFinishAction()
        }

        @JavascriptInterface
        fun getCurrentVersion(): String {
            val packageManager = <EMAIL>
            try {
                val packageInfo = packageManager.getPackageInfo(packageName, 0)
                val versionName = packageInfo.versionName
                return versionName.toString()
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
                return ""
            }
        }

        @JavascriptInterface
        fun openApp(packageName: String) {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                startActivity(intent)
            } else {
                jsToast("找不到指定App")
            }
        }

        /**
         * 获取设备的经纬度信息 (Asynchronous)
         */
        @SuppressLint("MissingPermission")
        @JavascriptInterface
        fun getLocationInfo(): String {
            val locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
            // 1. 尝试从 GPS Provider 获取
            val location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
            return if (location != null) {
                GsonUtils.toJson(mapOf(
                    "latitude" to location.latitude,
                    "longitude" to location.longitude
                ))
            } else {
                // 2. 如果失败，提示用户检查GPS
                GsonUtils.toJson(mapOf("error" to "无法获取当前位置，请确保GPS已开启"))
            }
        }

        @JavascriptInterface
        fun getBaiduLocation(callbackId: String) {
            if (isLocating) {
                // 如果正在定位，直接返回错误，避免并发定位
                val errorJson = GsonUtils.toJson(mapOf("error" to "正在定位，请稍后"))
                val escapedJson = JSONObject.quote(errorJson)
                val jsCode = "javascript:onNativeResult('$callbackId', JSON.parse($escapedJson))"
                webView.post {
                    webView.evaluateJavascript(jsCode, null)
                }
                return
            }
            mLocationCallbackId = callbackId
            isLocating = true
            if (mLocationClient?.isStarted == false) {
                mLocationClient?.start() // Start the client if it's not already running
            }
            mLocationClient?.requestLocation() // Request a new location update
        }


        @JavascriptInterface
        fun startCamera(callbackId: String) {
            jsCallbackId = callbackId
            jsCallType = "camera"

            currentPhotoFileName = "temp_photo_${System.currentTimeMillis()}.jpg"
            val photoFile = File(getExternalFilesDir(null), currentPhotoFileName!!)
            cameraImageUri = FileProvider.getUriForFile(
                this@MainActivity,
                "${packageName}.fileprovider",
                photoFile
            )

            val cameraIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, cameraImageUri)
            startActivityForResult(cameraIntent, REQUEST_CODE_CAMERA)
        }


        @JavascriptInterface
        fun getCapturedPhotoPaths(): String {
            return GsonUtils.toJson(capturedPhotoPaths)
        }

        @JavascriptInterface
        fun clearCapturedPhotos() {
            capturedPhotoPaths.clear()
        }


        @JavascriptInterface
        fun getSimInfo(): String {
            try {
                val tm = <EMAIL>(Context.TELEPHONY_SERVICE) as TelephonyManager
                val simInfo = mutableMapOf<String, Any?>()
                simInfo["simOperatorName"] = tm.simOperatorName          // 运营商名称
                simInfo["simCountryIso"] = tm.simCountryIso              // 国家ISO
                simInfo["simState"] = when (tm.simState) {               // SIM卡状态
                    TelephonyManager.SIM_STATE_READY -> "READY"
                    TelephonyManager.SIM_STATE_ABSENT -> "ABSENT"
                    TelephonyManager.SIM_STATE_PIN_REQUIRED -> "PIN_REQUIRED"
                    TelephonyManager.SIM_STATE_PUK_REQUIRED -> "PUK_REQUIRED"
                    TelephonyManager.SIM_STATE_NETWORK_LOCKED -> "NETWORK_LOCKED"
                    else -> "UNKNOWN"
                }
                // 以下字段在 Android 10 及以上获取不到（需系统权限）
                simInfo["line1Number"] = try {
                    tm.line1Number
                } catch (e: SecurityException) { "NO_PERMISSION" }
                simInfo["simOperator"] = tm.simOperator                  // MCC + MNC
                return GsonUtils.toJson(simInfo)
            } catch (e: Exception) {
                return GsonUtils.toJson(mapOf("error" to e.message))
            }
        }

        @JavascriptInterface
        fun getIotCardInfo(): String {
            val tm = <EMAIL>(Context.TELEPHONY_SERVICE) as TelephonyManager
            val info = mutableMapOf<String, Any?>()
            info["simState"] = when (tm.simState) {
                TelephonyManager.SIM_STATE_ABSENT -> "无SIM卡"
                TelephonyManager.SIM_STATE_READY -> "正常"
                else -> "未知/异常"
            }
            info["simOperator"] = tm.simOperator
            info["simOperatorName"] = tm.simOperatorName
            info["iccid"] = tm.simSerialNumber ?: "无"
            info["line1Number"] = tm.line1Number ?: "未知"
            info["isRoaming"] = tm.isNetworkRoaming
            info["networkType"] = tm.networkType
            // 尝试获取 IMSI（需要权限，部分IoT卡会失败）
            try {
                info["imsi"] = tm.subscriberId ?: "未知"
            } catch (e: SecurityException) {
                info["imsi"] = "权限不足"
            }
            return GsonUtils.toJson(info)
        }


    }

    /**
     * @param jsonStr 必须是一个json对象{"aaa":"xxx"}
     */
    private fun androidCallJs(jsonStr: String) {
        if (TextUtils.isEmpty(jsonStr)) return
        val jsScript = "javascript:callJS($jsonStr)"
        webView.post {
            // 仅4.4以上可以用，效率高，可方便获取返回值，但不能向下兼容
            webView.evaluateJavascript(jsScript) { value ->
                //此处为 js 返回的结果
                Log.d("test", "js返回结果: ---$value")
            }
        }
    }

    override fun onInfo(str: String) {
        Log.e("zp", "onInfo: $str")
        androidCallJs(str);
    }
}