{"logs": [{"outputFile": "com.beikes.anlugrid.test.app-mergeDebugAndroidTestResources-34:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1017,1105,1177,1254,1332,1408,1491,1560", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,1012,1100,1172,1249,1327,1403,1486,1555,1676"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "846,949,1042,1145,1248,1332,1408,1499,1590,1674,1758,1846,1918,1995,2073,2250,2333,2402", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "944,1037,1140,1243,1327,1403,1494,1585,1669,1753,1841,1913,1990,2068,2144,2328,2397,2518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,312,414,515,622,727,2149", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "197,307,409,510,617,722,841,2245"}}]}]}