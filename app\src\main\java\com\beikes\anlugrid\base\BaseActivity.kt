package com.beikes.anlugrid.base

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import com.beikes.anlugrid.defaultFullScreen
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import io.reactivex.disposables.CompositeDisposable

/**
 * @Package: com.beike.smartterminal.base
 * @CreateDate: 2024/8/16 11:00
 * @Author: zphaonu
 * @Contact: <EMAIL>
 * @Description:
 */
abstract class BaseActivity : ComponentActivity() {

    private var mCompositeDisposable = CompositeDisposable()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 根据配置进行全屏配置设置
        setFullScreen()
        // 统一申请所有权限
        checkAndRequestPermissions()
        setContentView(setLayoutId())
        init(savedInstanceState)
    }

    private val REQUEST_CODE_PERMISSIONS = 1001

    private fun checkAndRequestPermissions() {
        val permissionsNeeded = mutableListOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.CAMERA,
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.READ_PHONE_NUMBERS
        )

        val missingPermissions = permissionsNeeded.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, missingPermissions.toTypedArray(), REQUEST_CODE_PERMISSIONS)
        }
    }


    abstract fun setLayoutId(): Int

    abstract fun init(savedInstanceState: Bundle?)


    /**
     * 按需求设置全屏配置
     */
    private fun setFullScreen() {
        if(defaultFullScreen){
            ImmersionBar.with(this)
                .reset()
                .fullScreen(true)
                .hideBar(BarHide.FLAG_HIDE_BAR)
                .init()
        }else {
            // 让状态栏文字变成黑色（Android 6.0+）
            WindowCompat.getInsetsController(window, window.decorView).apply {
                isAppearanceLightStatusBars = true  // 黑色文字
            }
            window.statusBarColor = Color.WHITE
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mCompositeDisposable.clear()
    }
}
