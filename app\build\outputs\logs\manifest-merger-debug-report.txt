-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:122-88:20
	android:grantUriPermissions
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:84:13-47
	android:authorities
		INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:82:13-64
	android:exported
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:83:13-37
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:81:13-62
manifest
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:2:1-91:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fb9c4fa36bdbc4d694a1d1d74c2c92a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f36a950384bcdadd27c993c9266e995\transformed\play-services-vision-common-19.1.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4efd3923cc6b894a0445b3ae0fa263\transformed\play-services-clearcut-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1094cb4c93da902855ad0a1a148b739\transformed\play-services-phenotype-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca290cdd45d48f6916d9ffb59d12689c\transformed\play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2e8d97c2dca0edbcd04e10d19028142\transformed\play-services-flags-17.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\178296f1edf79dac8ec543f7d580153e\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:2:1-52:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd8e450179c94a5076653a8c92d3a411\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3350905c2fed8c6f17936db1b0755fc4\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\065d4d12167693814d5a0b2eaa96770a\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3cd7b8cbcab1ee6f2794cfc36c1f20c\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1c1a1964a69c304d583532b99ebca1\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e8c2e632d701b474e31e718e557e9f9\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e106a37a7d0809a9a4b3617d42d6079d\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\709b7de16bd677daaefc4eb04177b0ff\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45072afba101622b996b99e474e024f\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\397338603cccb1b805dcb3a62cfd8b2d\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e80450c13d74da4900f39c6d6bd6a449\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5091edadffdbdf0c57b7923b72215243\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e7844a39d79f4df54d1d067e842f37\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9197e08ab571c66e90ede67eaf3c0525\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e451c0a123d48a650076d0f078a82e\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af75497e31e7fa82f25ad4a8c00141f5\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7e771640f9f09d4631e53e48744d78\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9f195e70fff94e96313cb057e40c8\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26ab12328de0952d83168ae2781a086a\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9a3b6a15a999edf24db1b0eabf50b2a\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8d3fea1113dec55401802f80912d08a\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38dc1be28c85b2de6f02432f5825218c\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4f44ef8b334778c66476119df53161e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49b56954bcf745cd00f285cff3e320f0\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87dc805b2e0801895b0e3361e1d563cd\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b18c01417d1093075fd18bf9482f2b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fcc5532e0123b222679733ef4b092a7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\866a5a9de85a54345a7d544cde028c87\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a05c949941335414d9082928992e2a9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c701bbaf8f2a3c7dfcc105107a9624a\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27f06ea00de6cb9f53a74d7c763dd190\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd2e02955bec0d9af53448065531f37e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e25a1166c43eadea4614b2f84a9b48d4\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2036c4dcb7a131aa5550558481f760f2\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c5f4bf66c881433b81a00d50c457c09\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa4cfdcf89bbaa4056ed3b8522afa416\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edc9e9c54185697064685dcf63e3b38c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c3f1c2d4adad4b427b06773435314c\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3779a3df7501365faa633f23ed226567\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0a7c193071682a4213dfdea43cd8cdf\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c902bd1827820377fa0eea0a8305cd8\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\298f8e6788612f838a4a64dd099e3721\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb1ae7b2abdda65e32f8e9686fe65488\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f7bd63851b4f1d96674f2062a3974d7\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b995a8c8d204fa497763ee44a414af9\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95044e1675a52d370980adb077aa3939\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abcbd27caf75bc8a15b8cb8dec88a93b\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24f9368ba09794fa54252766be41f32c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a29b1a8703e813609f24078577e58997\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7431cef354ff41d008a18b6a518dab3f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede11e795a66b0bc03f5c4f546a8b79b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60383d84327c5fe053e066c33eb930e1\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\730ca8388db640975870b714aa53e2e7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.geyifeng.immersionbar:immersionbar:3.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68a636cf6bdd01eb99d5ead7cef0c40e\transformed\immersionbar-3.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf35e22b6be935c4d4ec6699109c9e07\transformed\rxandroid-2.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:2:1-42:12
MERGED from [com.baidu.lbsyun:BaiduMapSDK_Location:9.6.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab629a28bf14a6588d6ad7d916658c3\transformed\BaiduMapSDK_Location-9.6.4\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.camera
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:5:5-7:36
	android:required
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:7:9-33
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:6:9-47
uses-permission#android.permission.INTERNET
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:9:5-67
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:10:5-67
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:10:5-67
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:9:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:10:5-79
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:11:5-79
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:11:5-79
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:11:5-79
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:11:22-76
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:13:5-76
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:13:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:14:5-80
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:18:5-80
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:18:5-80
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:15:5-81
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:15:5-17:40
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:15:5-17:40
	tools:ignore
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:17:9-37
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:15:22-78
uses-permission#android.permission.CAMERA
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:16:5-65
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:16:22-62
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:17:5-76
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:17:22-73
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:20:5-71
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:20:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:21:5-80
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:21:22-77
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:23:5-83
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:19:5-83
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:19:5-83
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:23:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:24:5-81
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:24:22-78
uses-permission#android.permission.READ_PHONE_STATE
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:25:5-75
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:25:22-72
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:26:22-74
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:28:5-79
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:28:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:29:5-81
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:29:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:30:5-85
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:30:22-82
application
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:33:5-89:19
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:33:5-89:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fb9c4fa36bdbc4d694a1d1d74c2c92a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fb9c4fa36bdbc4d694a1d1d74c2c92a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f36a950384bcdadd27c993c9266e995\transformed\play-services-vision-common-19.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f36a950384bcdadd27c993c9266e995\transformed\play-services-vision-common-19.1.3\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4efd3923cc6b894a0445b3ae0fa263\transformed\play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4efd3923cc6b894a0445b3ae0fa263\transformed\play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1094cb4c93da902855ad0a1a148b739\transformed\play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1094cb4c93da902855ad0a1a148b739\transformed\play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca290cdd45d48f6916d9ffb59d12689c\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca290cdd45d48f6916d9ffb59d12689c\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\178296f1edf79dac8ec543f7d580153e\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\178296f1edf79dac8ec543f7d580153e\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:18:5-50:19
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:18:5-50:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24f9368ba09794fa54252766be41f32c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24f9368ba09794fa54252766be41f32c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede11e795a66b0bc03f5c4f546a8b79b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede11e795a66b0bc03f5c4f546a8b79b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:21:5-40:19
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:21:5-40:19
	android:extractNativeLibs
		INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:41:9-35
	android:label
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:39:9-41
	android:fullBackupContent
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:37:9-54
	android:roundIcon
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:40:9-40
	tools:targetApi
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:44:9-29
	android:icon
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:38:9-35
	android:allowBackup
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:35:9-35
	android:theme
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:42:9-50
	android:dataExtractionRules
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:36:9-65
	android:usesCleartextTraffic
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:43:9-44
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:34:9-38
activity#com.beikes.anlugrid.activity.SplashActivity
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:45:9-57:20
	android:screenOrientation
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:48:13-49
	android:exported
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:47:13-36
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:46:13-52
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:49:13-52:29
action#android.intent.action.MAIN
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:50:17-69
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:50:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:51:17-77
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:51:27-74
meta-data#android.app.lib_name
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:54:13-56:36
	android:value
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:56:17-33
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:55:17-52
activity#com.beikes.anlugrid.activity.MainActivity
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:58:9-61:61
	android:screenOrientation
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:61:13-49
	android:exported
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:60:13-36
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:59:13-50
receiver#com.beikes.anlugrid.receiver.BootReceiver
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:63:9-71:20
	android:enabled
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:65:13-35
	android:exported
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:66:13-36
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:64:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+category:name:android.intent.category.DEFAULT
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:67:13-70:29
	android:priority
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:67:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:68:17-79
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:68:25-76
category#android.intent.category.DEFAULT
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:69:17-76
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:69:27-73
service#com.tencent.smtt.export.external.DexClassLoaderProviderService
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:72:9-75:49
	android:process
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:75:13-38
	android:label
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:74:13-35
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:73:13-90
meta-data#com.baidu.lbsapi.API_KEY
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:76:9-79:21
	android:value
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:78:13-61
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:77:13-52
service#com.baidu.location.f
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:9-114
	android:process
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:77-102
	android:enabled
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:54-76
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:18-53
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:85:13-87:54
	android:resource
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:87:17-51
	android:name
		ADDED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:86:17-67
uses-sdk
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fb9c4fa36bdbc4d694a1d1d74c2c92a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fb9c4fa36bdbc4d694a1d1d74c2c92a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f36a950384bcdadd27c993c9266e995\transformed\play-services-vision-common-19.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-common:19.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f36a950384bcdadd27c993c9266e995\transformed\play-services-vision-common-19.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4efd3923cc6b894a0445b3ae0fa263\transformed\play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4efd3923cc6b894a0445b3ae0fa263\transformed\play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1094cb4c93da902855ad0a1a148b739\transformed\play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1094cb4c93da902855ad0a1a148b739\transformed\play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca290cdd45d48f6916d9ffb59d12689c\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca290cdd45d48f6916d9ffb59d12689c\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2e8d97c2dca0edbcd04e10d19028142\transformed\play-services-flags-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-flags:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2e8d97c2dca0edbcd04e10d19028142\transformed\play-services-flags-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\178296f1edf79dac8ec543f7d580153e\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\178296f1edf79dac8ec543f7d580153e\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:7:5-44
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:7:5-44
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd8e450179c94a5076653a8c92d3a411\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd8e450179c94a5076653a8c92d3a411\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3350905c2fed8c6f17936db1b0755fc4\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3350905c2fed8c6f17936db1b0755fc4\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\065d4d12167693814d5a0b2eaa96770a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\065d4d12167693814d5a0b2eaa96770a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3cd7b8cbcab1ee6f2794cfc36c1f20c\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3cd7b8cbcab1ee6f2794cfc36c1f20c\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1c1a1964a69c304d583532b99ebca1\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1c1a1964a69c304d583532b99ebca1\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e8c2e632d701b474e31e718e557e9f9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e8c2e632d701b474e31e718e557e9f9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e106a37a7d0809a9a4b3617d42d6079d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e106a37a7d0809a9a4b3617d42d6079d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\709b7de16bd677daaefc4eb04177b0ff\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\709b7de16bd677daaefc4eb04177b0ff\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45072afba101622b996b99e474e024f\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45072afba101622b996b99e474e024f\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\397338603cccb1b805dcb3a62cfd8b2d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\397338603cccb1b805dcb3a62cfd8b2d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e80450c13d74da4900f39c6d6bd6a449\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e80450c13d74da4900f39c6d6bd6a449\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5091edadffdbdf0c57b7923b72215243\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5091edadffdbdf0c57b7923b72215243\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e7844a39d79f4df54d1d067e842f37\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e7844a39d79f4df54d1d067e842f37\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9197e08ab571c66e90ede67eaf3c0525\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9197e08ab571c66e90ede67eaf3c0525\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e451c0a123d48a650076d0f078a82e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e451c0a123d48a650076d0f078a82e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af75497e31e7fa82f25ad4a8c00141f5\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af75497e31e7fa82f25ad4a8c00141f5\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7e771640f9f09d4631e53e48744d78\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7e771640f9f09d4631e53e48744d78\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9f195e70fff94e96313cb057e40c8\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b9f195e70fff94e96313cb057e40c8\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26ab12328de0952d83168ae2781a086a\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26ab12328de0952d83168ae2781a086a\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9a3b6a15a999edf24db1b0eabf50b2a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9a3b6a15a999edf24db1b0eabf50b2a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8d3fea1113dec55401802f80912d08a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8d3fea1113dec55401802f80912d08a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38dc1be28c85b2de6f02432f5825218c\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38dc1be28c85b2de6f02432f5825218c\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4f44ef8b334778c66476119df53161e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4f44ef8b334778c66476119df53161e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49b56954bcf745cd00f285cff3e320f0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49b56954bcf745cd00f285cff3e320f0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87dc805b2e0801895b0e3361e1d563cd\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87dc805b2e0801895b0e3361e1d563cd\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b18c01417d1093075fd18bf9482f2b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b18c01417d1093075fd18bf9482f2b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fcc5532e0123b222679733ef4b092a7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fcc5532e0123b222679733ef4b092a7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\866a5a9de85a54345a7d544cde028c87\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\866a5a9de85a54345a7d544cde028c87\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a05c949941335414d9082928992e2a9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a05c949941335414d9082928992e2a9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c701bbaf8f2a3c7dfcc105107a9624a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c701bbaf8f2a3c7dfcc105107a9624a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27f06ea00de6cb9f53a74d7c763dd190\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27f06ea00de6cb9f53a74d7c763dd190\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd2e02955bec0d9af53448065531f37e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd2e02955bec0d9af53448065531f37e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e25a1166c43eadea4614b2f84a9b48d4\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e25a1166c43eadea4614b2f84a9b48d4\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2036c4dcb7a131aa5550558481f760f2\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2036c4dcb7a131aa5550558481f760f2\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c5f4bf66c881433b81a00d50c457c09\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c5f4bf66c881433b81a00d50c457c09\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa4cfdcf89bbaa4056ed3b8522afa416\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa4cfdcf89bbaa4056ed3b8522afa416\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edc9e9c54185697064685dcf63e3b38c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edc9e9c54185697064685dcf63e3b38c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c3f1c2d4adad4b427b06773435314c\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8c3f1c2d4adad4b427b06773435314c\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3779a3df7501365faa633f23ed226567\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3779a3df7501365faa633f23ed226567\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0a7c193071682a4213dfdea43cd8cdf\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0a7c193071682a4213dfdea43cd8cdf\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c902bd1827820377fa0eea0a8305cd8\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c902bd1827820377fa0eea0a8305cd8\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\298f8e6788612f838a4a64dd099e3721\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\298f8e6788612f838a4a64dd099e3721\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb1ae7b2abdda65e32f8e9686fe65488\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb1ae7b2abdda65e32f8e9686fe65488\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f7bd63851b4f1d96674f2062a3974d7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f7bd63851b4f1d96674f2062a3974d7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b995a8c8d204fa497763ee44a414af9\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b995a8c8d204fa497763ee44a414af9\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95044e1675a52d370980adb077aa3939\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95044e1675a52d370980adb077aa3939\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abcbd27caf75bc8a15b8cb8dec88a93b\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abcbd27caf75bc8a15b8cb8dec88a93b\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24f9368ba09794fa54252766be41f32c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24f9368ba09794fa54252766be41f32c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a29b1a8703e813609f24078577e58997\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a29b1a8703e813609f24078577e58997\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7431cef354ff41d008a18b6a518dab3f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7431cef354ff41d008a18b6a518dab3f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede11e795a66b0bc03f5c4f546a8b79b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede11e795a66b0bc03f5c4f546a8b79b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60383d84327c5fe053e066c33eb930e1\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60383d84327c5fe053e066c33eb930e1\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\730ca8388db640975870b714aa53e2e7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\730ca8388db640975870b714aa53e2e7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.geyifeng.immersionbar:immersionbar:3.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68a636cf6bdd01eb99d5ead7cef0c40e\transformed\immersionbar-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.geyifeng.immersionbar:immersionbar:3.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68a636cf6bdd01eb99d5ead7cef0c40e\transformed\immersionbar-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf35e22b6be935c4d4ec6699109c9e07\transformed\rxandroid-2.1.0\AndroidManifest.xml:18:5-43
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf35e22b6be935c4d4ec6699109c9e07\transformed\rxandroid-2.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:6:5-8:41
MERGED from [com.baidu.lbsyun:BaiduMapSDK_Location:9.6.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab629a28bf14a6588d6ad7d916658c3\transformed\BaiduMapSDK_Location-9.6.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.baidu.lbsyun:BaiduMapSDK_Location:9.6.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab629a28bf14a6588d6ad7d916658c3\transformed\BaiduMapSDK_Location-9.6.4\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
queries
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:9:5-16:15
intent#action:name:android.intent.action.MAIN
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
intent#action:name:android.intent.action.VIEW
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
action#android.intent.action.VIEW
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:14:13-65
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:14:21-62
activity#com.blankj.utilcode.util.UtilsTransActivity4MainProcess
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:24:13-72
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:22:13-37
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:21:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:23:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:20:13-83
activity#com.blankj.utilcode.util.UtilsTransActivity
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
	android:multiprocess
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:29:13-40
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:31:13-72
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:28:13-37
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:27:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:30:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:26:13-71
provider#com.blankj.utilcode.util.UtilsFileProvider
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
	android:grantUriPermissions
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:37:13-47
	android:authorities
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:35:13-73
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:34:13-70
service#com.blankj.utilcode.util.MessengerUtils$ServerService
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:44:13-81
intent-filter#action:name:${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
intent-filter#action:name:com.beikes.anlugrid.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
action#${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:47:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:47:25-66
action#com.beikes.anlugrid.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:47:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:47:25-66
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede11e795a66b0bc03f5c4f546a8b79b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede11e795a66b0bc03f5c4f546a8b79b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.beikes.anlugrid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.beikes.anlugrid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
uses-permission#android.permission.INSTALL_PACKAGES
ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:12:5-14:47
	tools:ignore
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:14:9-44
	android:name
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:13:9-59
service#com.xuexiang.xupdate.service.DownloadService
ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:22:9-24:40
	android:exported
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:23:13-72
provider#com.xuexiang.xupdate.utils.UpdateFileProvider
ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:26:9-34:20
	android:grantUriPermissions
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:30:13-47
	android:authorities
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:28:13-70
	android:exported
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:27:13-73
activity#com.xuexiang.xupdate.widget.UpdateDialogActivity
ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:36:9-39:58
	android:exported
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:39:13-55
	android:name
		ADDED from [com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:37:13-76
