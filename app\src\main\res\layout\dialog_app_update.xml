<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="270dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/bg_dialog"
    android:orientation="vertical"
    tools:background="#CCCCCC">

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:layout_marginTop="8dp"
        android:layout_marginRight="8dp"
        android:padding="8dp" />


    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="15dp"
        android:gravity="center"
        android:text="发现新版本"
        android:textColor="#333333"
        android:textSize="18dp"
        android:textStyle="bold"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tvDialogContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="15dp"
        android:layout_marginTop="10dp"
        android:maxHeight="180dp"
        android:text="最新版本来啦，快更新体验~"
        android:textColor="#666666"
        android:textSize="16dp" />


    <TextView
        android:id="@+id/tvUpdate"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:layout_gravity="center"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/bg_btn"
        android:gravity="center"
        android:paddingHorizontal="28dp"
        android:text="立即更新"
        android:textColor="#FFFFFF"
        android:textSize="16dp" />

    <!--    <TextView-->
    <!--        android:id="@+id/tvNotAlert"-->
    <!--        android:layout_marginTop="@dimen/qb_px_6"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_gravity="center"-->
    <!--        android:layout_marginBottom="@dimen/qb_px_10"-->
    <!--        android:padding="@dimen/qb_px_4"-->
    <!--        android:text="下次不再提醒"-->
    <!--        android:textColor="@color/color_999999"-->
    <!--        android:textSize="@dimen/qb_px_13" />-->


</LinearLayout>