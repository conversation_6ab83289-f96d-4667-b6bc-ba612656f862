{"logs": [{"outputFile": "com.beikes.anlugrid.app-mergeDebugResources-54:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2934,3036,3133,3237,3341,3446,13389", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2929,3031,3128,3232,3336,3441,3557,13485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd8e450179c94a5076653a8c92d3a411\\transformed\\appcompat-1.2.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,12996", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,13078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,998,1083,1156,1233,1312,1389,1468,1538", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,78,76,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,993,1078,1151,1228,1307,1384,1463,1533,1651"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3562,3659,5921,6018,6119,6205,6282,12654,12746,12831,12911,13083,13156,13233,13312,13490,13569,13639", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,78,76,78,69,117", "endOffsets": "3654,3741,6013,6114,6200,6277,6368,12741,12826,12906,12991,13151,13228,13307,13384,13564,13634,13752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\912538d559762e4041e291f7f0f02de2\\transformed\\play-services-base-18.5.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3746,3853,4011,4138,4248,4402,4529,4641,4873,5022,5129,5289,5416,5565,5708,5776,5841", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "3848,4006,4133,4243,4397,4524,4636,4738,5017,5124,5284,5411,5560,5703,5771,5836,5916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b10a22010430b8fe9d7a17232c1c060\\transformed\\play-services-basement-18.4.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4743", "endColumns": "129", "endOffsets": "4868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3350905c2fed8c6f17936db1b0755fc4\\transformed\\material3-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,289,417,534,633,727,838,974,1094,1236,1321,1421,1516,1614,1730,1855,1960,2101,2241,2374,2554,2679,2799,2924,3046,3142,3240,3358,3488,3588,3690,3799,3941,4090,4199,4302,4379,4478,4576,4685,4774,4860,4967,5047,5130,5227,5330,5423,5521,5608,5716,5813,5915,6048,6128,6237", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "167,284,412,529,628,722,833,969,1089,1231,1316,1416,1511,1609,1725,1850,1955,2096,2236,2369,2549,2674,2794,2919,3041,3137,3235,3353,3483,3583,3685,3794,3936,4085,4194,4297,4374,4473,4571,4680,4769,4855,4962,5042,5125,5222,5325,5418,5516,5603,5711,5808,5910,6043,6123,6232,6331"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6373,6490,6607,6735,6852,6951,7045,7156,7292,7412,7554,7639,7739,7834,7932,8048,8173,8278,8419,8559,8692,8872,8997,9117,9242,9364,9460,9558,9676,9806,9906,10008,10117,10259,10408,10517,10620,10697,10796,10894,11003,11092,11178,11285,11365,11448,11545,11648,11741,11839,11926,12034,12131,12233,12366,12446,12555", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "6485,6602,6730,6847,6946,7040,7151,7287,7407,7549,7634,7734,7829,7927,8043,8168,8273,8414,8554,8687,8867,8992,9112,9237,9359,9455,9553,9671,9801,9901,10003,10112,10254,10403,10512,10615,10692,10791,10889,10998,11087,11173,11280,11360,11443,11540,11643,11736,11834,11921,12029,12126,12228,12361,12441,12550,12649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ce1c1a1964a69c304d583532b99ebca1\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "13757,13847", "endColumns": "89,91", "endOffsets": "13842,13934"}}]}]}