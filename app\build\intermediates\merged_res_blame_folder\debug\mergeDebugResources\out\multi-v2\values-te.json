{"logs": [{"outputFile": "com.beikes.anlugrid.app-mergeDebugResources-54:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\912538d559762e4041e291f7f0f02de2\\transformed\\play-services-base-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3782,3894,4048,4178,4293,4430,4555,4660,4898,5047,5159,5312,5444,5595,5758,5822,5892", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "3889,4043,4173,4288,4425,4550,4655,4755,5042,5154,5307,5439,5590,5753,5817,5887,5971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b10a22010430b8fe9d7a17232c1c060\\transformed\\play-services-basement-18.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4760", "endColumns": "137", "endOffsets": "4893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd8e450179c94a5076653a8c92d3a411\\transformed\\appcompat-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,2921"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,13252", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,13328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2947,3055,3157,3258,3364,3471,13651", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "2942,3050,3152,3253,3359,3466,3590,13747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3350905c2fed8c6f17936db1b0755fc4\\transformed\\material3-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,315,429,559,663,762,878,1019,1131,1274,1358,1461,1557,1655,1771,1901,2009,2158,2305,2438,2634,2762,2878,2999,3136,3233,3330,3455,3583,3689,3795,3901,4044,4194,4302,4406,4482,4581,4682,4798,4892,4984,5091,5171,5254,5355,5483,5577,5689,5777,5888,5990,6107,6230,6310,6417", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "179,310,424,554,658,757,873,1014,1126,1269,1353,1456,1552,1650,1766,1896,2004,2153,2300,2433,2629,2757,2873,2994,3131,3228,3325,3450,3578,3684,3790,3896,4039,4189,4297,4401,4477,4576,4677,4793,4887,4979,5086,5166,5249,5350,5478,5572,5684,5772,5883,5985,6102,6225,6305,6412,6509"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6447,6576,6707,6821,6951,7055,7154,7270,7411,7523,7666,7750,7853,7949,8047,8163,8293,8401,8550,8697,8830,9026,9154,9270,9391,9528,9625,9722,9847,9975,10081,10187,10293,10436,10586,10694,10798,10874,10973,11074,11190,11284,11376,11483,11563,11646,11747,11875,11969,12081,12169,12280,12382,12499,12622,12702,12809", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "6571,6702,6816,6946,7050,7149,7265,7406,7518,7661,7745,7848,7944,8042,8158,8288,8396,8545,8692,8825,9021,9149,9265,9386,9523,9620,9717,9842,9970,10076,10182,10288,10431,10581,10689,10793,10869,10968,11069,11185,11279,11371,11478,11558,11641,11742,11870,11964,12076,12164,12275,12377,12494,12617,12697,12804,12901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,389,489,578,667,763,851,935,1019,1109,1186,1268,1348,1427,1504,1573", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "198,287,384,484,573,662,758,846,930,1014,1104,1181,1263,1343,1422,1499,1568,1685"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3595,3693,5976,6073,6173,6262,6351,12906,12994,13078,13162,13333,13410,13492,13572,13752,13829,13898", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "3688,3777,6068,6168,6257,6346,6442,12989,13073,13157,13247,13405,13487,13567,13646,13824,13893,14010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ce1c1a1964a69c304d583532b99ebca1\\transformed\\foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "14015,14103", "endColumns": "87,94", "endOffsets": "14098,14193"}}]}]}