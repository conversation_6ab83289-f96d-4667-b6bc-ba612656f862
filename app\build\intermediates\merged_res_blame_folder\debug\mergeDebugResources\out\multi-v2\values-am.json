{"logs": [{"outputFile": "com.beikes.anlugrid.app-mergeDebugResources-54:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b10a22010430b8fe9d7a17232c1c060\\transformed\\play-services-basement-18.4.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4495", "endColumns": "131", "endOffsets": "4622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2723,2816,2916,3013,3112,3208,3310,12577", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "2811,2911,3008,3107,3203,3305,3405,12673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd8e450179c94a5076653a8c92d3a411\\transformed\\appcompat-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,12209", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,12284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,934,1015,1085,1158,1231,1303,1383,1448", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,929,1010,1080,1153,1226,1298,1378,1443,1559"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3410,3493,5576,5668,5764,5846,5924,11890,11972,12050,12128,12289,12359,12432,12505,12678,12758,12823", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "3488,3565,5663,5759,5841,5919,6002,11967,12045,12123,12204,12354,12427,12500,12572,12753,12818,12934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3350905c2fed8c6f17936db1b0755fc4\\transformed\\material3-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4334,4418,4503,4605,4686,4769,4869,4966,5061,5156,5241,5343,5442,5541,5659,5740,5841", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4329,4413,4498,4600,4681,4764,4864,4961,5056,5151,5236,5338,5437,5536,5654,5735,5836,5933"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6007,6118,6228,6336,6443,6537,6627,6734,6862,6972,7101,7183,7281,7368,7461,7571,7690,7793,7916,8041,8165,8313,8429,8542,8656,8771,8859,8954,9064,9183,9278,9380,9482,9602,9728,9832,9928,10002,10095,10187,10286,10370,10455,10557,10638,10721,10821,10918,11013,11108,11193,11295,11394,11493,11611,11692,11793", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "6113,6223,6331,6438,6532,6622,6729,6857,6967,7096,7178,7276,7363,7456,7566,7685,7788,7911,8036,8160,8308,8424,8537,8651,8766,8854,8949,9059,9178,9273,9375,9477,9597,9723,9827,9923,9997,10090,10182,10281,10365,10450,10552,10633,10716,10816,10913,11008,11103,11188,11290,11389,11488,11606,11687,11788,11885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ce1c1a1964a69c304d583532b99ebca1\\transformed\\foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,85", "endOffsets": "137,223"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "12939,13026", "endColumns": "86,85", "endOffsets": "13021,13107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\912538d559762e4041e291f7f0f02de2\\transformed\\play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3570,3672,3811,3933,4035,4162,4285,4393,4627,4755,4858,5003,5126,5261,5388,5448,5505", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "3667,3806,3928,4030,4157,4280,4388,4490,4750,4853,4998,5121,5256,5383,5443,5500,5571"}}]}]}