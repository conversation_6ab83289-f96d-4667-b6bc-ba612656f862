{"logs": [{"outputFile": "com.beikes.anlugrid.app-mergeDebugResources-54:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\27f06ea00de6cb9f53a74d7c763dd190\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "636,640", "startColumns": "4,4", "startOffsets": "33947,34112", "endColumns": "53,66", "endOffsets": "33996,34174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b10a22010430b8fe9d7a17232c1c060\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "672,721", "startColumns": "4,4", "startOffsets": "35802,39679", "endColumns": "67,166", "endOffsets": "35865,39841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\38dc1be28c85b2de6f02432f5825218c\\transformed\\appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2539,2555,2561,4538,4554", "startColumns": "4,4,4,4,4", "startOffsets": "158818,159243,159421,232189,232600", "endLines": "2554,2560,2570,4553,4557", "endColumns": "24,24,24,24,24", "endOffsets": "159238,159416,159700,232595,232722"}}, {"source": "D:\\work\\jiuyexiaozhi-app\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "293,57,105,153,201,247,336", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "330,99,147,195,241,287,373"}, "to": {"startLines": "381,432,433,434,445,446,449", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17218,20776,20823,20870,21614,21659,21825", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "17255,20818,20865,20912,21654,21699,21862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\912538d559762e4041e291f7f0f02de2\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "392,393,394,395,396,397,398,399,713,714,715,716,717,718,719,720,722,723,724,725,726,727,728,729,730,4110,4503", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18032,18122,18202,18292,18382,18462,18543,18623,38639,38744,38925,39050,39157,39337,39460,39576,39846,40034,40139,40320,40445,40620,40768,40831,40893,216960,231310", "endLines": "392,393,394,395,396,397,398,399,713,714,715,716,717,718,719,720,722,723,724,725,726,727,728,729,730,4122,4521", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "18117,18197,18287,18377,18457,18538,18618,18698,38739,38920,39045,39152,39332,39455,39571,39674,40029,40134,40315,40440,40615,40763,40826,40888,40967,217270,231722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "279,375,376,390,391,422,423,534,535,536,537,538,539,540,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,586,587,588,637,638,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,674,704,705,706,707,708,709,710,806,2213,2214,2218,2219,2223,2368,2369,3061,3085,3751,3784,3805,3838", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12540,16739,16811,17901,17966,20056,20125,27549,27619,27687,27759,27829,27890,27964,28821,28882,28943,29005,29069,29131,29192,29260,29360,29420,29486,29559,29628,29685,29737,31003,31075,31151,34001,34036,34363,34418,34481,34536,34594,34652,34713,34776,34833,34884,34934,34995,35052,35118,35152,35187,35943,38009,38076,38148,38217,38286,38360,38432,46197,139721,139838,140039,140149,140350,151863,151935,175612,176752,203360,205091,205772,206454", "endLines": "279,375,376,390,391,422,423,534,535,536,537,538,539,540,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,586,587,588,637,638,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,674,704,705,706,707,708,709,710,806,2213,2217,2218,2222,2223,2368,2369,3066,3094,3783,3804,3837,3843", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "12595,16806,16894,17961,18027,20120,20183,27614,27682,27754,27824,27885,27959,28032,28877,28938,29000,29064,29126,29187,29255,29355,29415,29481,29554,29623,29680,29732,29794,31070,31146,31211,34031,34066,34413,34476,34531,34589,34647,34708,34771,34828,34879,34929,34990,35047,35113,35147,35182,35217,36008,38071,38143,38212,38281,38355,38427,38515,46263,139833,140034,140144,140345,140474,151930,151997,175810,177048,205086,205767,206449,206616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7b9f195e70fff94e96313cb057e40c8\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "643,664", "startColumns": "4,4", "startOffsets": "34275,35342", "endColumns": "41,59", "endOffsets": "34312,35397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ce1c1a1964a69c304d583532b99ebca1\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "810,811", "startColumns": "4,4", "startOffsets": "46441,46497", "endColumns": "55,54", "endOffsets": "46492,46547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\68a636cf6bdd01eb99d5ead7cef0c40e\\transformed\\immersionbar-3.2.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,105,164,223", "endColumns": "49,58,58,54", "endOffsets": "100,159,218,273"}, "to": {"startLines": "622,632,633,634", "startColumns": "4,4,4,4", "startOffsets": "33183,33719,33778,33837", "endColumns": "49,58,58,54", "endOffsets": "33228,33773,33832,33887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\496d4b805756ebce5e91f7821d3db38d\\transformed\\XUpdate-2.1.5\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,62,82,90,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,176,239,300,364,425,482,539,601,657,718,773,830,885,939,1009,1064,1121,1176,1254,1351,1473,1569,1657,1754,1839,1928,2007,2096,2183,2258,2384,2464,2571,2651,2728,2793,2847,2903,2978,3065,3120,3186,3277,3424,4273,5287,5826,6417", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,61,81,89,101,121", "endColumns": "61,58,62,60,63,60,56,56,61,55,60,54,56,54,53,69,54,56,54,77,96,121,95,87,96,84,88,78,88,86,74,125,79,106,79,76,64,53,55,74,86,54,65,90,146,12,12,12,12,24", "endOffsets": "112,171,234,295,359,420,477,534,596,652,713,768,825,880,934,1004,1059,1116,1171,1249,1346,1468,1564,1652,1749,1834,1923,2002,2091,2178,2253,2379,2459,2566,2646,2723,2788,2842,2898,2973,3060,3115,3181,3272,3419,4268,5282,5821,6412,7179"}, "to": {"startLines": "450,451,452,453,454,455,573,574,575,576,577,578,579,580,581,582,583,584,585,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,2370,2385,2405,2413,4859", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21867,21929,21988,22051,22112,22176,30252,30309,30366,30428,30484,30545,30600,30657,30712,30766,30836,30891,30948,46552,46630,46727,46849,46945,47033,47130,47215,47304,47383,47472,47559,47634,47760,47840,47947,48027,48104,48169,48223,48279,48354,48441,48496,48562,48653,152002,152784,153648,154187,244508", "endLines": "450,451,452,453,454,455,573,574,575,576,577,578,579,580,581,582,583,584,585,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,2384,2404,2412,2424,4878", "endColumns": "61,58,62,60,63,60,56,56,61,55,60,54,56,54,53,69,54,56,54,77,96,121,95,87,96,84,88,78,88,86,74,125,79,106,79,76,64,53,55,74,86,54,65,90,146,12,12,12,12,24", "endOffsets": "21924,21983,22046,22107,22171,22232,30304,30361,30423,30479,30540,30595,30652,30707,30761,30831,30886,30943,30998,46625,46722,46844,46940,47028,47125,47210,47299,47378,47467,47554,47629,47755,47835,47942,48022,48099,48164,48218,48274,48349,48436,48491,48557,48648,48795,152779,153643,154182,154773,245270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d6e451c0a123d48a650076d0f078a82e\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "629", "startColumns": "4", "startOffsets": "33568", "endColumns": "65", "endOffsets": "33629"}}, {"source": "D:\\work\\jiuyexiaozhi-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "46", "endOffsets": "59"}, "to": {"startLines": "703", "startColumns": "4", "startOffsets": "37962", "endColumns": "46", "endOffsets": "38004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\edc9e9c54185697064685dcf63e3b38c\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "666", "startColumns": "4", "startOffsets": "35456", "endColumns": "49", "endOffsets": "35501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ede11e795a66b0bc03f5c4f546a8b79b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "702", "startColumns": "4", "startOffsets": "37879", "endColumns": "82", "endOffsets": "37957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd8e450179c94a5076653a8c92d3a411\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "63,124,125,318,365,366,367,368,369,370,371,372,373,374,377,378,379,380,382,383,384,385,386,387,388,389,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,424,425,426,427,428,429,430,431,435,436,437,438,439,440,441,442,443,444,447,448,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,541,542,543,544,545,546,547,548,549,565,566,567,568,569,570,571,572,621,623,624,625,631,641,642,644,661,668,669,670,671,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,801,850,851,852,853,854,855,863,864,868,872,876,881,887,894,898,902,907,911,915,919,923,927,931,937,941,947,951,957,961,966,970,973,977,983,987,993,997,1003,1006,1010,1014,1018,1022,1026,1027,1028,1029,1032,1035,1038,1041,1045,1046,1047,1048,1049,1052,1054,1056,1058,1063,1064,1068,1074,1078,1079,1081,1092,1093,1097,1103,1107,1108,1109,1113,1140,1144,1145,1149,1177,1346,1372,1541,1567,1598,1606,1612,1626,1648,1653,1658,1668,1677,1686,1690,1697,1705,1712,1713,1722,1725,1728,1732,1736,1740,1743,1744,1749,1754,1764,1769,1776,1782,1783,1786,1790,1795,1797,1799,1802,1805,1807,1811,1814,1821,1824,1827,1831,1833,1837,1839,1841,1843,1847,1855,1863,1875,1881,1890,1893,1904,1907,1908,1913,1914,1942,2011,2081,2082,2092,2101,2102,2104,2108,2111,2114,2117,2120,2123,2126,2129,2133,2136,2139,2142,2146,2149,2153,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2179,2181,2182,2183,2184,2185,2186,2187,2188,2190,2191,2193,2194,2196,2198,2199,2201,2202,2203,2204,2205,2206,2208,2209,2210,2211,2212,2224,2226,2228,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2244,2245,2246,2247,2248,2249,2250,2252,2256,2261,2262,2263,2264,2265,2266,2270,2271,2272,2273,2275,2277,2279,2281,2283,2284,2285,2286,2288,2290,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2306,2307,2308,2309,2311,2313,2314,2316,2317,2319,2321,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2336,2337,2338,2339,2341,2342,2343,2344,2345,2347,2349,2351,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2425,2500,2503,2506,2509,2523,2529,2571,2600,2627,2636,2698,3057,3095,3733,4074,4098,4104,4123,4144,4268,4440,4446,4462,4468,4522,4561,4627,4663,4797,4809,4835", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2809,5350,5395,14217,16062,16117,16176,16238,16302,16372,16433,16508,16584,16661,16899,16984,17066,17142,17260,17337,17415,17521,17627,17706,17786,17843,18703,18777,18852,18917,18983,19043,19104,19176,19249,19316,19384,19443,19502,19561,19620,19679,19733,19787,19840,19894,19948,20002,20188,20262,20341,20414,20488,20559,20631,20703,20917,20974,21032,21105,21179,21253,21328,21400,21473,21543,21704,21764,22237,22306,22375,22445,22519,22595,22659,22736,22812,22889,22954,23023,23100,23175,23244,23312,23389,23455,23516,23613,23678,23747,23846,23917,23976,24034,24091,24150,24214,24285,24357,24429,24501,24573,24640,24708,24776,24835,24898,24962,25052,25143,25203,25269,25336,25402,25472,25536,25589,25656,25717,25784,25897,25955,26018,26083,26148,26223,26296,26368,26417,26478,26539,26600,26662,26726,26790,26854,26919,26982,27042,27103,27169,27228,27288,27350,27421,27481,28037,28123,28210,28300,28387,28475,28557,28640,28730,29799,29851,29909,29954,30020,30084,30141,30198,33126,33233,33281,33330,33685,34179,34226,34317,35222,35559,35623,35685,35745,36013,36087,36157,36235,36289,36359,36444,36492,36538,36599,36662,36728,36792,36863,36926,36991,37055,37116,37177,37229,37302,37376,37445,37520,37594,37668,37809,45970,49541,49619,49709,49797,49893,49983,50565,50654,50901,51182,51434,51719,52112,52589,52811,53033,53309,53536,53766,53996,54226,54456,54683,55102,55328,55753,55983,56411,56630,56913,57121,57252,57479,57905,58130,58557,58778,59203,59323,59599,59900,60224,60515,60829,60966,61097,61202,61444,61611,61815,62023,62294,62406,62518,62623,62740,62954,63100,63240,63326,63674,63762,64008,64426,64675,64757,64855,65447,65547,65799,66223,66478,66572,66661,66898,68922,69164,69266,69519,71675,82116,83632,94171,95699,97456,98082,98502,99563,100828,101084,101320,101867,102361,102966,103164,103744,104308,104683,104801,105339,105496,105692,105965,106221,106391,106532,106596,106961,107328,108004,108268,108606,108959,109053,109239,109545,109807,109932,110059,110298,110509,110628,110821,110998,111453,111634,111756,112015,112128,112315,112417,112524,112653,112928,113436,113932,114809,115103,115673,115822,116554,116726,116810,117146,117238,118802,124048,129437,129499,130077,130661,130752,130865,131094,131254,131406,131577,131743,131912,132079,132242,132485,132655,132828,132999,133273,133472,133677,134007,134091,134187,134283,134381,134481,134583,134685,134787,134889,134991,135091,135187,135299,135428,135551,135682,135813,135911,136025,136119,136259,136393,136489,136601,136701,136817,136913,137025,137125,137265,137401,137565,137695,137853,138003,138144,138288,138423,138535,138685,138813,138941,139077,139209,139339,139469,139581,140479,140625,140769,140907,140973,141063,141139,141243,141333,141435,141543,141651,141751,141831,141923,142021,142131,142183,142261,142367,142459,142563,142673,142795,142958,143204,143284,143384,143474,143584,143674,143915,144009,144115,144207,144307,144419,144533,144649,144765,144859,144973,145085,145187,145307,145429,145511,145615,145735,145861,145959,146053,146141,146253,146369,146491,146603,146778,146894,146980,147072,147184,147308,147375,147501,147569,147697,147841,147969,148038,148133,148248,148361,148460,148569,148680,148791,148892,148997,149097,149227,149318,149441,149535,149647,149733,149837,149933,150021,150139,150243,150347,150473,150561,150669,150769,150859,150969,151053,151155,151239,151293,151357,151463,151549,151659,151743,154778,157394,157512,157627,157707,158068,158301,159705,161049,162410,162798,165573,175477,177053,202788,215747,216498,216760,217275,217654,221932,229336,229565,230012,230227,231727,232847,235873,237264,241912,242252,243563", "endLines": "63,124,125,318,365,366,367,368,369,370,371,372,373,374,377,378,379,380,382,383,384,385,386,387,388,389,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,424,425,426,427,428,429,430,431,435,436,437,438,439,440,441,442,443,444,447,448,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,541,542,543,544,545,546,547,548,549,565,566,567,568,569,570,571,572,621,623,624,625,631,641,642,644,661,668,669,670,671,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,801,850,851,852,853,854,862,863,867,871,875,880,886,893,897,901,906,910,914,918,922,926,930,936,940,946,950,956,960,965,969,972,976,982,986,992,996,1002,1005,1009,1013,1017,1021,1025,1026,1027,1028,1031,1034,1037,1040,1044,1045,1046,1047,1048,1051,1053,1055,1057,1062,1063,1067,1073,1077,1078,1080,1091,1092,1096,1102,1106,1107,1108,1112,1139,1143,1144,1148,1176,1345,1371,1540,1566,1597,1605,1611,1625,1647,1652,1657,1667,1676,1685,1689,1696,1704,1711,1712,1721,1724,1727,1731,1735,1739,1742,1743,1748,1753,1763,1768,1775,1781,1782,1785,1789,1794,1796,1798,1801,1804,1806,1810,1813,1820,1823,1826,1830,1832,1836,1838,1840,1842,1846,1854,1862,1874,1880,1889,1892,1903,1906,1907,1912,1913,1918,2010,2080,2081,2091,2100,2101,2103,2107,2110,2113,2116,2119,2122,2125,2128,2132,2135,2138,2141,2145,2148,2152,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2178,2180,2181,2182,2183,2184,2185,2186,2187,2189,2190,2192,2193,2195,2197,2198,2200,2201,2202,2203,2204,2205,2207,2208,2209,2210,2211,2212,2225,2227,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2243,2244,2245,2246,2247,2248,2249,2251,2255,2259,2261,2262,2263,2264,2265,2269,2270,2271,2272,2274,2276,2278,2280,2282,2283,2284,2285,2287,2289,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2305,2306,2307,2308,2310,2312,2313,2315,2316,2318,2320,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2335,2336,2337,2338,2340,2341,2342,2343,2344,2346,2348,2350,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2499,2502,2505,2508,2522,2528,2538,2599,2626,2635,2697,3056,3060,3122,3750,4097,4103,4109,4143,4267,4287,4445,4449,4467,4502,4533,4626,4646,4717,4808,4834,4841", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2859,5390,5439,14253,16112,16171,16233,16297,16367,16428,16503,16579,16656,16734,16979,17061,17137,17213,17332,17410,17516,17622,17701,17781,17838,17896,18772,18847,18912,18978,19038,19099,19171,19244,19311,19379,19438,19497,19556,19615,19674,19728,19782,19835,19889,19943,19997,20051,20257,20336,20409,20483,20554,20626,20698,20771,20969,21027,21100,21174,21248,21323,21395,21468,21538,21609,21759,21820,22301,22370,22440,22514,22590,22654,22731,22807,22884,22949,23018,23095,23170,23239,23307,23384,23450,23511,23608,23673,23742,23841,23912,23971,24029,24086,24145,24209,24280,24352,24424,24496,24568,24635,24703,24771,24830,24893,24957,25047,25138,25198,25264,25331,25397,25467,25531,25584,25651,25712,25779,25892,25950,26013,26078,26143,26218,26291,26363,26412,26473,26534,26595,26657,26721,26785,26849,26914,26977,27037,27098,27164,27223,27283,27345,27416,27476,27544,28118,28205,28295,28382,28470,28552,28635,28725,28816,29846,29904,29949,30015,30079,30136,30193,30247,33178,33276,33325,33376,33714,34221,34270,34358,35249,35618,35680,35740,35797,36082,36152,36230,36284,36354,36439,36487,36533,36594,36657,36723,36787,36858,36921,36986,37050,37111,37172,37224,37297,37371,37440,37515,37589,37663,37804,37874,46018,49614,49704,49792,49888,49978,50560,50649,50896,51177,51429,51714,52107,52584,52806,53028,53304,53531,53761,53991,54221,54451,54678,55097,55323,55748,55978,56406,56625,56908,57116,57247,57474,57900,58125,58552,58773,59198,59318,59594,59895,60219,60510,60824,60961,61092,61197,61439,61606,61810,62018,62289,62401,62513,62618,62735,62949,63095,63235,63321,63669,63757,64003,64421,64670,64752,64850,65442,65542,65794,66218,66473,66567,66656,66893,68917,69159,69261,69514,71670,82111,83627,94166,95694,97451,98077,98497,99558,100823,101079,101315,101862,102356,102961,103159,103739,104303,104678,104796,105334,105491,105687,105960,106216,106386,106527,106591,106956,107323,107999,108263,108601,108954,109048,109234,109540,109802,109927,110054,110293,110504,110623,110816,110993,111448,111629,111751,112010,112123,112310,112412,112519,112648,112923,113431,113927,114804,115098,115668,115817,116549,116721,116805,117141,117233,117511,124043,129432,129494,130072,130656,130747,130860,131089,131249,131401,131572,131738,131907,132074,132237,132480,132650,132823,132994,133268,133467,133672,134002,134086,134182,134278,134376,134476,134578,134680,134782,134884,134986,135086,135182,135294,135423,135546,135677,135808,135906,136020,136114,136254,136388,136484,136596,136696,136812,136908,137020,137120,137260,137396,137560,137690,137848,137998,138139,138283,138418,138530,138680,138808,138936,139072,139204,139334,139464,139576,139716,140620,140764,140902,140968,141058,141134,141238,141328,141430,141538,141646,141746,141826,141918,142016,142126,142178,142256,142362,142454,142558,142668,142790,142953,143110,143279,143379,143469,143579,143669,143910,144004,144110,144202,144302,144414,144528,144644,144760,144854,144968,145080,145182,145302,145424,145506,145610,145730,145856,145954,146048,146136,146248,146364,146486,146598,146773,146889,146975,147067,147179,147303,147370,147496,147564,147692,147836,147964,148033,148128,148243,148356,148455,148564,148675,148786,148887,148992,149092,149222,149313,149436,149530,149642,149728,149832,149928,150016,150134,150238,150342,150468,150556,150664,150764,150854,150964,151048,151150,151234,151288,151352,151458,151544,151654,151738,151858,157389,157507,157622,157702,158063,158296,158813,161044,162405,162793,165568,175472,175607,178405,203355,216493,216755,216955,217649,221927,222533,229560,229711,230222,231305,232034,235868,236612,239390,242247,243558,243761"}}, {"source": "D:\\work\\jiuyexiaozhi-app\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "59", "endColumns": "89", "endOffsets": "144"}, "to": {"startLines": "2260", "startColumns": "4", "startOffsets": "143115", "endColumns": "88", "endOffsets": "143199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1fb9c4fa36bdbc4d694a1d1d74c2c92a\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,63,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,124,130,131,132,133,136,139,142,143,146,149,150,151,152,153,156,159,160,161,162,168,173,176,179,180,181,186,187,188,191,194,195,198,201,204,207,208,209,212,215,216,221,222,228,233,236,239,240,241,242,243,244,245,246,247,248,249,250,266,272,273,274,275,276,283,289,290,291,294,299,300,308,309,310,311,312,313,314,315,324,325,326,332,333,339,343,344,345,346,347,356,360,361,362,380,566,694,700,704,874,1026,1039,1055,1080,1103,1106,1109,1112,1141,1168,1185,1271,1279,1292,1308,1312,1342,1355,1359,1369,1379,1423,1436,1440,1443,1459,1500,1535,1542,1559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,339,395,581,642,933,985,1035,1088,1136,1187,1242,1302,1367,1426,1488,1540,1601,1663,1709,1842,1894,1944,1995,2402,2714,2759,2818,3015,3072,3267,3448,3502,3559,3751,3809,4005,4061,4255,4312,4363,4585,4637,4692,4882,5098,5148,5200,5256,5462,5523,5583,5653,5786,5917,6045,6113,6242,6368,6430,6493,6561,6628,6751,6876,6943,7008,7073,7362,7543,7664,7785,7851,7918,8128,8197,8263,8388,8514,8581,8707,8834,8959,9086,9142,9207,9333,9456,9521,9729,9796,10084,10264,10384,10504,10569,10631,10693,10757,10819,10878,10938,10999,11060,11119,11179,11870,12121,12172,12221,12269,12327,12619,12849,12896,12956,13062,13242,13296,13631,13685,13741,13787,13834,13885,13944,13996,14326,14385,14439,14677,14732,15022,15161,15207,15262,15307,15351,15699,15836,15877,15922,16859,25449,31222,31597,31764,39466,46265,46962,47713,48588,49458,49524,49603,49678,51026,52013,52976,56913,57318,57789,58580,58743,60104,60668,60821,61280,61698,63711,64248,64398,64518,65165,66854,68275,68628,69370", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,63,64,69,70,75,80,81,82,87,88,93,94,99,100,101,107,108,109,114,120,121,122,123,129,130,131,132,135,138,141,142,145,148,149,150,151,152,155,158,159,160,161,167,172,175,178,179,180,185,186,187,190,193,194,197,200,203,206,207,208,211,214,215,220,221,227,232,235,238,239,240,241,242,243,244,245,246,247,248,249,265,271,272,273,274,275,282,288,289,290,293,298,299,307,308,309,310,311,312,313,314,323,324,325,331,332,338,342,343,344,345,346,355,359,360,361,379,565,693,699,703,873,1025,1038,1054,1079,1102,1105,1108,1111,1140,1167,1184,1270,1278,1291,1307,1311,1341,1354,1358,1368,1378,1422,1435,1439,1442,1458,1499,1534,1541,1558,1561", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "334,390,576,637,928,980,1030,1083,1131,1182,1237,1297,1362,1421,1483,1535,1596,1658,1704,1837,1889,1939,1990,2397,2709,2754,2813,3010,3067,3262,3443,3497,3554,3746,3804,4000,4056,4250,4307,4358,4580,4632,4687,4877,5093,5143,5195,5251,5457,5518,5578,5648,5781,5912,6040,6108,6237,6363,6425,6488,6556,6623,6746,6871,6938,7003,7068,7357,7538,7659,7780,7846,7913,8123,8192,8258,8383,8509,8576,8702,8829,8954,9081,9137,9202,9328,9451,9516,9724,9791,10079,10259,10379,10499,10564,10626,10688,10752,10814,10873,10933,10994,11055,11114,11174,11865,12116,12167,12216,12264,12322,12614,12844,12891,12951,13057,13237,13291,13626,13680,13736,13782,13829,13880,13939,13991,14321,14380,14434,14672,14727,15017,15156,15202,15257,15302,15346,15694,15831,15872,15917,16854,25444,31217,31592,31759,39461,46260,46957,47708,48583,49453,49519,49598,49673,51021,52008,52971,56908,57313,57784,58575,58738,60099,60663,60816,61275,61693,63706,64243,64393,64513,65160,66849,68270,68623,69365,69466"}, "to": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,64,65,66,71,72,77,82,83,84,89,90,95,96,101,102,103,109,110,111,116,122,123,126,127,133,134,135,136,139,142,145,146,149,152,153,154,155,156,159,162,163,164,165,171,176,179,182,183,184,189,190,191,194,197,198,201,204,207,210,211,212,215,218,219,224,225,231,236,239,242,243,244,245,246,247,248,249,250,251,252,253,269,275,276,277,278,280,287,293,294,295,298,303,304,312,313,314,315,316,317,319,320,329,330,331,337,338,344,348,349,350,351,352,361,639,662,3067,3123,3288,3416,3422,3426,3575,3720,3844,3860,3885,3908,3911,3914,3917,3944,3971,3988,4288,4296,4309,4325,4329,4359,4372,4376,4386,4396,4450,4534,4558,4647,4718,4755,4790,4842,4879", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,434,490,676,737,1028,1080,1130,1183,1231,1282,1337,1397,1462,1521,1583,1635,1696,1758,1804,1937,1989,2039,2090,2497,2864,2909,2968,3165,3222,3417,3598,3652,3709,3901,3959,4155,4211,4405,4462,4513,4735,4787,4842,5032,5248,5298,5444,5500,5706,5767,5827,5897,6030,6161,6289,6357,6486,6612,6674,6737,6805,6872,6995,7120,7187,7252,7317,7606,7787,7908,8029,8095,8162,8372,8441,8507,8632,8758,8825,8951,9078,9203,9330,9386,9451,9577,9700,9765,9973,10040,10328,10508,10628,10748,10813,10875,10937,11001,11063,11122,11182,11243,11304,11363,11423,12083,12334,12385,12434,12482,12600,12892,13122,13169,13229,13335,13515,13569,13904,13958,14014,14060,14107,14158,14258,14310,14640,14699,14753,14991,15046,15248,15387,15433,15488,15533,15577,15925,34071,35254,175815,178410,184528,190193,190568,190735,195965,202091,206621,207372,208226,209096,209162,209241,209316,210100,210991,211810,222538,222943,223414,224205,224368,225729,226293,226446,226905,227323,229716,232039,232727,236617,239395,240138,241559,243766,245275", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,126,132,133,134,135,138,141,144,145,148,151,152,153,154,155,158,161,162,163,164,170,175,178,181,182,183,188,189,190,193,196,197,200,203,206,209,210,211,214,217,218,223,224,230,235,238,241,242,243,244,245,246,247,248,249,250,251,252,268,274,275,276,277,278,286,292,293,294,297,302,303,311,312,313,314,315,316,317,319,328,329,330,336,337,343,347,348,349,350,351,360,364,639,662,3084,3287,3415,3421,3425,3574,3719,3732,3859,3884,3907,3910,3913,3916,3943,3970,3987,4073,4295,4308,4324,4328,4358,4371,4375,4385,4395,4439,4461,4537,4560,4662,4754,4789,4796,4858,4881", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "429,485,671,732,1023,1075,1125,1178,1226,1277,1332,1392,1457,1516,1578,1630,1691,1753,1799,1932,1984,2034,2085,2492,2804,2904,2963,3160,3217,3412,3593,3647,3704,3896,3954,4150,4206,4400,4457,4508,4730,4782,4837,5027,5243,5293,5345,5495,5701,5762,5822,5892,6025,6156,6284,6352,6481,6607,6669,6732,6800,6867,6990,7115,7182,7247,7312,7601,7782,7903,8024,8090,8157,8367,8436,8502,8627,8753,8820,8946,9073,9198,9325,9381,9446,9572,9695,9760,9968,10035,10323,10503,10623,10743,10808,10870,10932,10996,11058,11117,11177,11238,11299,11358,11418,12078,12329,12380,12429,12477,12535,12887,13117,13164,13224,13330,13510,13564,13899,13953,14009,14055,14102,14153,14212,14305,14635,14694,14748,14986,15041,15243,15382,15428,15483,15528,15572,15920,16057,34107,35294,176747,184523,190188,190563,190730,195960,202086,202783,207367,208221,209091,209157,209236,209311,210095,210986,211805,215742,222938,223409,224200,224363,225724,226288,226441,226900,227318,229331,230007,232184,232842,237259,240133,241554,241907,244503,245371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,626,627,628,630,635,667,711,712,731,732,733,734,735,797,798,799,800,802,803,804,805,807,808,809,1919,1935,1938", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31216,31275,31334,31394,31454,31514,31574,31634,31694,31754,31814,31874,31934,31993,32053,32113,32173,32233,32293,32353,32413,32473,32533,32593,32652,32712,32772,32831,32890,32949,33008,33067,33381,33455,33513,33634,33892,35506,38520,38585,40972,41038,41139,41197,41249,45750,45812,45866,45916,46023,46069,46115,46157,46268,46315,46351,117516,118496,118607", "endLines": "589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,626,627,628,630,635,667,711,712,731,732,733,734,735,797,798,799,800,802,803,804,805,807,808,809,1921,1937,1941", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "31270,31329,31389,31449,31509,31569,31629,31689,31749,31809,31869,31929,31988,32048,32108,32168,32228,32288,32348,32408,32468,32528,32588,32647,32707,32767,32826,32885,32944,33003,33062,33121,33450,33508,33563,33680,33942,35554,38580,38634,41033,41134,41192,41244,41304,45807,45861,45911,45965,46064,46110,46152,46192,46310,46346,46436,117623,118602,118797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e25a1166c43eadea4614b2f84a9b48d4\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "665", "startColumns": "4", "startOffsets": "35402", "endColumns": "53", "endOffsets": "35451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3350905c2fed8c6f17936db1b0755fc4\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "673,736,737,738,739,740,741,742,743,744,745,748,749,750,751,752,753,754,755,756,757,758,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,1922,1932", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "35870,41309,41397,41483,41564,41648,41717,41782,41865,41971,42057,42177,42231,42300,42361,42430,42519,42614,42688,42785,42878,42976,43125,43216,43304,43400,43498,43562,43630,43717,43811,43878,43950,44022,44123,44232,44308,44377,44425,44491,44555,44629,44686,44743,44815,44865,44919,44990,45061,45131,45200,45258,45334,45405,45479,45565,45615,45685,117628,118343", "endLines": "673,736,737,738,739,740,741,742,743,744,747,748,749,750,751,752,753,754,755,756,757,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,1931,1934", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "35938,41392,41478,41559,41643,41712,41777,41860,41966,42052,42172,42226,42295,42356,42425,42514,42609,42683,42780,42873,42971,43120,43211,43299,43395,43493,43557,43625,43712,43806,43873,43945,44017,44118,44227,44303,44372,44420,44486,44550,44624,44681,44738,44810,44860,44914,44985,45056,45126,45195,45253,45329,45400,45474,45560,45610,45680,45745,118338,118491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3779a3df7501365faa633f23ed226567\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "663", "startColumns": "4", "startOffsets": "35299", "endColumns": "42", "endOffsets": "35337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\459484f9ece9bcfb108010f0bb082e93\\transformed\\utilcodex-1.31.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "13", "endColumns": "12", "endOffsets": "791"}, "to": {"startLines": "838", "startColumns": "4", "startOffsets": "48800", "endLines": "849", "endColumns": "12", "endOffsets": "49536"}}]}]}