import java.text.SimpleDateFormat
import java.util.*
import javax.xml.parsers.DocumentBuilderFactory

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

// 定义读取strings.xml中app_name的函数
fun getAppName(): String {
    val stringsFile = file("src/main/res/values/strings.xml")
    val doc = DocumentBuilderFactory.newInstance().newDocumentBuilder().parse(stringsFile)
    val nodes = doc.getElementsByTagName("string")

    for (i in 0 until nodes.length) {
        val node = nodes.item(i)
        if (node.attributes.getNamedItem("name").textContent == "app_name") {
            return node.textContent
        }
    }
    return "" // 默认值
}

android {
    signingConfigs {
        getByName("debug") {
            storeFile =
                file("D:\\work\\jingzhou\\work_space_yy\\jiuyexiaozhi-app\\jiuyexiaozhi.jks")
            storePassword = "jiuyexiaozhi"
            keyAlias = "key0"
            keyPassword = "jiuyexiaozhi"
        }
    }
    namespace = "com.beikes.anlugrid"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.beikes.anlugrid"
        minSdk = 30
        targetSdk = 35
        versionCode = 5
        versionName = "1.6"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    applicationVariants.all {
        outputs.all {
            val variantOutput = this as com.android.build.gradle.internal.api.BaseVariantOutputImpl
            val sdf = SimpleDateFormat("yyyyMMdd_HHmm", Locale.getDefault())
            val formattedDate = sdf.format(Date())
            variantOutput.outputFileName = "${getAppName()}v${versionName}_$formattedDate.apk"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        compose = true
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.play.services.vision.common)
    implementation(libs.play.services.location)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)

    implementation(files("libs/AlipayFace_V1.0.2.jar"))
    implementation(files("libs/tbs_sdk_thirdapp_v4.3.0.386_44286_sharewithdownloadwithfile_withoutGame_obfs_20230210_114429.jar"))
    implementation(libs.immersionbar)
    implementation(libs.rxjava)
    implementation(libs.rxandroid)
    implementation(libs.utilcodex)
    implementation(libs.okhttp)
    implementation(libs.converter.gson)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.xupdate)
    implementation("com.google.android.gms:play-services-location:21.0.1")
    implementation("com.baidu.lbsyun:BaiduMapSDK_Location:9.6.4")
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
}