1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.beikes.anlugrid"
4    android:versionCode="5"
5    android:versionName="1.6" >
6
7    <uses-sdk
8        android:minSdkVersion="30"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.camera"
12-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:6:9-47
13        android:required="false" />
13-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:7:9-33
14
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:9:5-67
15-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:9:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:10:5-79
16-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:10:22-76
17    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
17-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:11:5-79
17-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:11:22-76
18    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
18-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:12:5-76
18-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:12:22-73
19    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
19-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:13:5-76
19-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:13:22-73
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:14:5-80
20-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:14:22-77
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:15:5-81
21-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:15:22-78
22    <uses-permission android:name="android.permission.CAMERA" />
22-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:16:5-65
22-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:16:22-62
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:17:5-76
23-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:17:22-73
24    <uses-permission android:name="android.permission.RECORD_AUDIO" />
24-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:20:5-71
24-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:20:22-68
25    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
25-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:21:5-80
25-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:21:22-77
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:22:5-77
26-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:22:22-74
27    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
27-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:23:5-83
27-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:23:22-80
28    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
28-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:24:5-81
28-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:24:22-78
29    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
29-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:25:5-75
29-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:25:22-72
30    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
30-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:26:5-77
30-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:26:22-74
31    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
31-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:28:5-79
31-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:28:22-76
32    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
32-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:29:5-81
32-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:29:22-78
33    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
33-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:30:5-85
33-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:30:22-82
34
35    <queries>
35-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:9:5-16:15
36        <intent>
36-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
37            <action android:name="android.intent.action.MAIN" />
37-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:50:17-69
37-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:50:25-66
38        </intent>
39        <intent>
39-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
40            <action android:name="android.intent.action.VIEW" />
40-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:14:13-65
40-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:14:21-62
41        </intent>
42    </queries>
43
44    <permission
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.beikes.anlugrid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.beikes.anlugrid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
49-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:12:5-14:47
49-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:13:9-59
50
51    <application
51-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:33:5-89:19
52        android:name="com.beikes.anlugrid.MyApplication"
52-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:34:9-38
53        android:allowBackup="true"
53-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:35:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3604e631eb8339ca47ab80b99f81649b\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:36:9-65
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:37:9-54
59        android:icon="@mipmap/fav"
59-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:38:9-35
60        android:label="@string/app_name"
60-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:39:9-41
61        android:roundIcon="@mipmap/fav"
61-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:40:9-40
62        android:supportsRtl="true"
62-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:41:9-35
63        android:theme="@style/Theme.Jiuyexiaozhi"
63-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:42:9-50
64        android:usesCleartextTraffic="true" >
64-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:43:9-44
65        <activity
65-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:45:9-57:20
66            android:name="com.beikes.anlugrid.activity.SplashActivity"
66-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:46:13-52
67            android:exported="true"
67-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:47:13-36
68            android:screenOrientation="portrait" >
68-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:48:13-49
69            <intent-filter>
69-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:49:13-52:29
70                <action android:name="android.intent.action.MAIN" />
70-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:50:17-69
70-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:50:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:51:17-77
72-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:51:27-74
73            </intent-filter>
74
75            <meta-data
75-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:54:13-56:36
76                android:name="android.app.lib_name"
76-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:55:17-52
77                android:value="" />
77-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:56:17-33
78        </activity>
79        <activity
79-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:58:9-61:61
80            android:name="com.beikes.anlugrid.activity.MainActivity"
80-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:59:13-50
81            android:exported="true"
81-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:60:13-36
82            android:screenOrientation="portrait" />
82-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:61:13-49
83
84        <receiver
84-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:63:9-71:20
85            android:name="com.beikes.anlugrid.receiver.BootReceiver"
85-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:64:13-50
86            android:enabled="true"
86-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:65:13-35
87            android:exported="true" >
87-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:66:13-36
88            <intent-filter android:priority="1000" >
88-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:67:13-70:29
88-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:67:28-51
89                <action android:name="android.intent.action.BOOT_COMPLETED" />
89-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:68:17-79
89-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:68:25-76
90
91                <category android:name="android.intent.category.DEFAULT" />
91-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:69:17-76
91-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:69:27-73
92            </intent-filter>
93        </receiver>
94
95        <service
95-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:72:9-75:49
96            android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService"
96-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:73:13-90
97            android:label="dexopt"
97-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:74:13-35
98            android:process=":dexopt" />
98-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:75:13-38
99
100        <meta-data
100-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:76:9-79:21
101            android:name="com.baidu.lbsapi.API_KEY"
101-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:77:13-52
102            android:value="lsbrg4WEKQ7aeolbnzfFMonRaCPlzzcA" >
102-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:78:13-61
103        </meta-data>
104
105        <service
105-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:9-114
106            android:name="com.baidu.location.f"
106-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:18-53
107            android:enabled="true"
107-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:54-76
108            android:process=":remote" >
108-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:80:77-102
109        </service>
110
111        <provider
112            android:name="androidx.core.content.FileProvider"
112-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:81:13-62
113            android:authorities="com.beikes.anlugrid.fileprovider"
113-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:82:13-64
114            android:exported="false"
114-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:83:13-37
115            android:grantUriPermissions="true" >
115-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:84:13-47
116            <meta-data
116-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:85:13-87:54
117                android:name="android.support.FILE_PROVIDER_PATHS"
117-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:86:17-67
118                android:resource="@xml/file_paths" />
118-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:87:17-51
119        </provider>
120
121        <activity
121-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
122            android:name="com.google.android.gms.common.api.GoogleApiActivity"
122-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
123            android:exported="false"
123-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
124            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
124-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\912538d559762e4041e291f7f0f02de2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
125
126        <meta-data
126-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
127            android:name="com.google.android.gms.version"
127-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
128            android:value="@integer/google_play_services_version" />
128-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b10a22010430b8fe9d7a17232c1c060\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
129
130        <activity
130-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
131            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
131-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:20:13-83
132            android:configChanges="orientation|keyboardHidden|screenSize"
132-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:21:13-74
133            android:exported="false"
133-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:22:13-37
134            android:theme="@style/ActivityTranslucent"
134-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:23:13-55
135            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
135-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:24:13-72
136        <activity
136-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
137            android:name="com.blankj.utilcode.util.UtilsTransActivity"
137-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:26:13-71
138            android:configChanges="orientation|keyboardHidden|screenSize"
138-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:27:13-74
139            android:exported="false"
139-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:28:13-37
140            android:multiprocess="true"
140-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:29:13-40
141            android:theme="@style/ActivityTranslucent"
141-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:30:13-55
142            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
142-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:31:13-72
143
144        <provider
144-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
145            android:name="com.blankj.utilcode.util.UtilsFileProvider"
145-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:34:13-70
146            android:authorities="com.beikes.anlugrid.utilcode.fileprovider"
146-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:35:13-73
147            android:exported="false"
147-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:36:13-37
148            android:grantUriPermissions="true" >
148-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:37:13-47
149            <meta-data
149-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:85:13-87:54
150                android:name="android.support.FILE_PROVIDER_PATHS"
150-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:86:17-67
151                android:resource="@xml/util_code_provider_paths" />
151-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:87:17-51
152        </provider>
153
154        <service
154-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
155            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
155-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:44:13-81
156            android:exported="false" >
156-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:45:13-37
157            <intent-filter>
157-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
158                <action android:name="com.beikes.anlugrid.messenger" />
158-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:47:17-69
158-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\459484f9ece9bcfb108010f0bb082e93\transformed\utilcodex-1.31.1\AndroidManifest.xml:47:25-66
159            </intent-filter>
160        </service>
161
162        <activity
162-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
163            android:name="androidx.compose.ui.tooling.PreviewActivity"
163-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
164            android:exported="true" />
164-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b88f73f70ac52b79993a80c9f67c8dae\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
165        <activity
165-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
166            android:name="androidx.activity.ComponentActivity"
166-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
167            android:exported="true" />
167-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89c34fa4a0bdc56537e345fac6da3c7b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
168
169        <provider
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
170            android:name="androidx.startup.InitializationProvider"
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
171            android:authorities="com.beikes.anlugrid.androidx-startup"
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
172            android:exported="false" >
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
173            <meta-data
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.emoji2.text.EmojiCompatInitializer"
174-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
175                android:value="androidx.startup" />
175-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6169d865fce36360709d4373e140d380\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
176            <meta-data
176-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
177                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
177-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
178                android:value="androidx.startup" />
178-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8acdf4f567285979ea82643751be35aa\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
179            <meta-data
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
181                android:value="androidx.startup" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
182        </provider>
183
184        <receiver
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
185            android:name="androidx.profileinstaller.ProfileInstallReceiver"
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
186            android:directBootAware="false"
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
187            android:enabled="true"
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
188            android:exported="true"
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
189            android:permission="android.permission.DUMP" >
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
190            <intent-filter>
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
191                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
192            </intent-filter>
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
194                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
195            </intent-filter>
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
197                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
198            </intent-filter>
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
200                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a18e55e1a4a17de40542a31051b2f345\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
201            </intent-filter>
202        </receiver>
203
204        <service
204-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:22:9-24:40
205            android:name="com.xuexiang.xupdate.service.DownloadService"
205-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:23:13-72
206            android:exported="false" />
206-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:24:13-37
207
208        <provider
208-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:26:9-34:20
209            android:name="com.xuexiang.xupdate.utils.UpdateFileProvider"
209-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:27:13-73
210            android:authorities="com.beikes.anlugrid.updateFileProvider"
210-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:28:13-70
211            android:exported="false"
211-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:29:13-37
212            android:grantUriPermissions="true" >
212-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:30:13-47
213            <meta-data
213-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:85:13-87:54
214                android:name="android.support.FILE_PROVIDER_PATHS"
214-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:86:17-67
215                android:resource="@xml/update_file_paths" />
215-->D:\work\jiuyexiaozhi-app\app\src\main\AndroidManifest.xml:87:17-51
216        </provider>
217
218        <activity
218-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:36:9-39:58
219            android:name="com.xuexiang.xupdate.widget.UpdateDialogActivity"
219-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:37:13-76
220            android:exported="false"
220-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:38:13-37
221            android:theme="@style/XUpdate_DialogTheme" />
221-->[com.github.xuexiangjys:XUpdate:2.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\496d4b805756ebce5e91f7821d3db38d\transformed\XUpdate-2.1.5\AndroidManifest.xml:39:13-55
222    </application>
223
224</manifest>
