<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8"/>
    <title>Android WebView JS 接口调用示例</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <style>
        body {
          font-family: Arial, sans-serif;
          padding: 20px;
        }
        button {
          margin: 10px 0;
          padding: 10px 20px;
          width: 100%;
          font-size: 16px;
        }
        pre {
          background-color: #f4f4f4;
          padding: 10px;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
    </style>
</head>
<body>
<h2>Android WebView JS 接口调用示例</h2>

<!-- 设备信息 -->
<button onclick="getDeviceInfo()">获取设备信息 (getDeviceInfo)</button>
<pre id="deviceInfo"></pre>

<!-- 获取设备 ID -->
<button onclick="getDeviceId()">获取设备ID (getDeviceId)</button>
<pre id="deviceId"></pre>

<!-- 获取版本号 -->
<button onclick="getCurrentVersion()">获取当前版本号 (getCurrentVersion)</button>
<pre id="version"></pre>

<!-- 显示 Toast -->
<button onclick="jsToast()">显示 Toast (jsToast)</button>

<!-- 页面刷新 -->
<button onclick="reloadPage()">页面刷新 (reload)</button>

<!-- 退出 App -->
<button onclick="exitApp()">退出应用 (exit)</button>

<!-- 打开其他 App -->
<div>
    <input
            type="text"
            value="com.beikes.jiuyexiaozhi"
            id="packageNameInput"
            placeholder="请输入要打开的 App 包名，例如：com.tencent.mm"
            style="width: 100%; padding: 10px; margin-top: 10px"
    />
    <button onclick="openOtherApp()">打开指定 App</button>
</div>

<!-- 定位信息 -->
<button onclick="getLocationInfo()">获取系统经纬度信息 (getLocationInfo)</button>
<pre id="locationInfo"></pre>

<button onclick="getBaiduLocation()">获取百度经纬度信息 (getBaiduLocation)</button>
<pre id="baiduLocation"></pre>

<!-- 拍照和文件上传 -->
<button onclick="takePhoto()">呼起相机拍照</button>
<pre id="photoResult"></pre>

<!-- 原有呼起相机按钮下方，增加清空按钮 -->
<button onclick="clearPhotos()">清空缓存图片</button>


<input type="file"  />


<input type="file" id="fileInput" multiple />
<ul id="fileList"></ul>

<!-- SIM 卡信息 -->
<button onclick="getSimInfo()">获取 SIM 卡信息</button>
<pre id="simInfo"></pre>

<button onclick="getIotCardInfo()">获取物联网卡信息 (getIotCardInfo)</button>
<pre id="iotCardInfo"></pre>


<!-- 判断是否为安卓环境 -->
<button onclick="checkAndroidEnvironment()">判断是否为安卓环境</button>
<pre id="androidCheckResult"></pre>

<script>

       // 原生回调统一入口
    // 修改 onNativeResult 中 camera 分支
    function onNativeResult(callbackId, data) {
      try {
        const json = typeof data === "string" ? data : data;

        if (callbackId.includes("baiduLoc")) {
          document.getElementById("baiduLocation").innerText = JSON.stringify(json, null, 2);
        } else if (callbackId.includes("camera")) {
          // 缓存路径并预览
          cachedPhotoPaths.push(data);
          const container = document.getElementById("photoResult");
          // 新增一行分隔符和图片元素
          const hr = document.createElement("hr");
          const textNode = document.createTextNode("图片路径: " + data);
          const imgEl = document.createElement("img");

          // 如果是本地文件路径，确保带file://协议，否则Chrome不显示
          imgEl.src = data.startsWith("file://") ? data : "file://" + data;
          imgEl.style.maxWidth = "100%";
          imgEl.style.marginTop = "10px";

          container.appendChild(hr);
          container.appendChild(textNode);
          container.appendChild(imgEl);
        } else  if (callbackId.includes("file")) {
            if (Array.isArray(data)) {
              document.getElementById("fileResult").innerHTML = data.map((p,i) => `文件${i+1}: ${p}`).join("<br>");
            } else {
              document.getElementById("fileResult").innerText = "文件路径: " + data;
            }
          } else if (callbackId.includes("sim")) {
          document.getElementById("simInfo").innerText = JSON.stringify(json, null, 2);
        }
      } catch (e) {
        console.error("onNativeResult 解析失败:", e);
      }
    }

    // 清空缓存图片函数
    function clearPhotos() {
      cachedPhotoPaths = [];
      const container = document.getElementById("photoResult");
      container.innerHTML = "";  // 清空所有内容
    }

    function isAndroidWebView() {
      const ua = navigator.userAgent.toLowerCase();
      return ua.includes("android") && typeof window.android !== "undefined";
    }

    function checkAndroidEnvironment() {
      const resultEl = document.getElementById("androidCheckResult");
      if (isAndroidWebView()) {
        resultEl.innerText = "当前运行环境：✅ 安卓 WebView";
        resultEl.style.color = "green";
      } else {
        resultEl.innerText = "当前运行环境：❌ 非安卓环境";
        resultEl.style.color = "red";
      }
    }

    function getDeviceInfo() {
      try {
        const result = window.android.getDeviceInfo();
        document.getElementById("deviceInfo").innerText = result;
      } catch (e) {
        console.error(e);
      }
    }

    function getDeviceId() {
      try {
        const result = window.android.getDeviceId();
        document.getElementById("deviceId").innerText = result;
      } catch (e) {
        console.error(e);
      }
    }

    function getCurrentVersion() {
      try {
        const result = window.android.getCurrentVersion();
        document.getElementById("version").innerText = result;
      } catch (e) {
        console.error(e);
      }
    }

    function jsToast() {
      try {
        window.android.jsToast("这是来自 H5 的 Toast 消息");
      } catch (e) {
        console.error(e);
      }
    }

    function reloadPage() {
      try {
        window.android.reload();
      } catch (e) {
        console.error(e);
      }
    }

    function exitApp() {
      try {
        window.android.exit();
      } catch (e) {
        console.error(e);
      }
    }

    function openOtherApp() {
      const packageName = document.getElementById("packageNameInput").value.trim();
      if (!packageName) {
        alert("请输入有效包名");
        return;
      }
      try {
        window.android.openApp(packageName);
      } catch (e) {
        alert("无法打开应用：" + e.message);
      }
    }

    function getLocationInfo() {
      try {
        const result = window.android.getLocationInfo();
        document.getElementById("locationInfo").innerText = result;
      } catch (e) {
        console.error(e);
        alert("获取系统位置信息失败：" + e.message);
      }
    }

    function getBaiduLocation() {
      const callbackId = "baiduLoc_" + Date.now();
      try {
        window.android.getBaiduLocation(callbackId);
      } catch (e) {
        alert("调用百度定位失败：" + e.message);
      }
    }


    // 维护一个数组存储图片路径（缓存图片路径）
    let cachedPhotoPaths = [];

    function takePhoto() {
      const callbackId = "camera_" + Date.now();
      try {
        window.android.startCamera(callbackId);
      } catch (e) {
        alert("无法打开相机：" + e.message);
      }
    }


    function getSimInfo() {
      try {
        const result = window.android.getSimInfo();
        document.getElementById("simInfo").innerText = JSON.stringify(JSON.parse(result), null, 2);
      } catch (e) {
        alert("获取 SIM 卡信息失败：" + e.message);
      }
    }

    function getIotCardInfo() {
      try {
        const result = window.android.getIotCardInfo();
        document.getElementById("iotCardInfo").innerText = JSON.stringify(JSON.parse(result), null, 2);
      } catch (e) {
        alert("获取 IoT 卡信息失败：" + e.message);
      }
    }

const input = document.getElementById('fileInput')
  const fileList = document.getElementById('fileList')

  input.addEventListener('change', () => {
    fileList.innerHTML = ''  // 清空旧列表
    const files = input.files
    if (!files.length) {
      fileList.innerHTML = '<li>未选择文件</li>'
      return
    }

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const li = document.createElement('li')
      li.textContent = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`
      fileList.appendChild(li)
    }
  })
</script>
</body>
</html>
