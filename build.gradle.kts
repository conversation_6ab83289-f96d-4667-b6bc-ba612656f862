// Top-level build file where you can add configuration options common to all sub-projects/modules.
// 顶级 build.gradle.kts（项目级）
buildscript {
    repositories {
        mavenCentral() // 保留默认仓库（可选）
        google() // 保留默认仓库（可选）
    }
}

plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose) apply false
}
