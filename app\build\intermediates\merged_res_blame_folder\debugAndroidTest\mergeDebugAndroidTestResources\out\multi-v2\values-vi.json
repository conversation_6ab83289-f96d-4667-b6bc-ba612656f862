{"logs": [{"outputFile": "com.beikes.anlugrid.test.app-mergeDebugAndroidTestResources-34:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,606,719,2150", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "197,299,398,498,601,714,830,2246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1028,1119,1191,1267,1344,1420,1497,1563", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1023,1114,1186,1262,1339,1415,1492,1558,1672"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,931,1017,1123,1223,1315,1400,1493,1587,1668,1758,1849,1921,1997,2074,2251,2328,2394", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "926,1012,1118,1218,1310,1395,1488,1582,1663,1753,1844,1916,1992,2069,2145,2323,2389,2503"}}]}]}