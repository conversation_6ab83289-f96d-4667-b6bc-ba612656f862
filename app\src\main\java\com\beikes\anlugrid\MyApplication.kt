package com.beikes.anlugrid

import android.app.Application
import android.util.Log
import com.beikes.anlugrid.utils.AlipayManager
import com.tencent.smtt.export.external.TbsCoreSettings
import com.tencent.smtt.sdk.QbSdk
import com.tencent.smtt.sdk.TbsDownloader
import com.tencent.smtt.sdk.TbsListener


/**
 * @Package: com.beike.smartterminal
 * @CreateDate: 2024/8/16 11:10
 * @Author: zphaonu
 * @Contact: <EMAIL>
 * @Description:
 */
class MyApplication : Application() {

    private val TAG = MyApplication::class.java.simpleName

    companion object {
        lateinit var mInstance: MyApplication
        val LOG_TAG get() = mInstance.getString(R.string.app_name)
    }

    lateinit var mAlipayManager: AlipayManager

    override fun onCreate() {
        super.onCreate()
        initX5Core()
        mInstance = this;
        mAlipayManager = AlipayManager(this.applicationContext)
    }


    private fun initX5Core() {
        Log.e(TAG, "开始进行x5内核初始化")
        // 在调用TBS初始化、创建WebView之前进行如下配置
        val map = hashMapOf<String, Any>()
        map[TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER] = true
        map[TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE] = true
        QbSdk.initTbsSettings(map)
        //配置允许移动网络下载内核（大小 40-50 MB）
        QbSdk.setDownloadWithoutWifi(true)
        QbSdk.setTbsListener(object : TbsListener {
            override fun onDownloadFinish(i: Int) {
                Log.e(TAG, "x5内核下载完成, $i")
            }

            override fun onInstallFinish(i: Int) {
                Log.e(TAG, "x5内核安装完成, $i")
            }

            override fun onDownloadProgress(i: Int) {
                Log.e(TAG, "开始进行x5内核下载，进度$i")
            }
        })
        QbSdk.initX5Environment(this, object : QbSdk.PreInitCallback {
            override fun onCoreInitFinished() {
                // 内核初始化完成，可能为系统内核，也可能为系统内核
                Log.e(TAG, "onCoreInitFinished")
            }

            /**
             * 预初始化结束
             * 由于X5内核体积较大，需要依赖网络动态下发，所以当内核不存在的时候，默认会回调false，此时将会使用系统内核代替
             * @param isX5 是否使用X5内核
             */
            override fun onViewInitFinished(isX5: Boolean) {
                Log.e(TAG, "onViewInitFinished: $isX5")
                if (!isX5 && TbsDownloader.isDownloading()) {
                    TbsDownloader.startDownload(applicationContext)
                }
            }
        })
    }
}
