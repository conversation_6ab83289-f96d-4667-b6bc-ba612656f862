<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\jiuyexiaozhi-app\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\jiuyexiaozhi-app\app\src\main\res"><file name="bg_activate_content" path="D:\work\jiuyexiaozhi-app\app\src\main\res\drawable\bg_activate_content.xml" qualifiers="" type="drawable"/><file name="bg_btn" path="D:\work\jiuyexiaozhi-app\app\src\main\res\drawable\bg_btn.xml" qualifiers="" type="drawable"/><file name="bg_dialog" path="D:\work\jiuyexiaozhi-app\app\src\main\res\drawable\bg_dialog.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\work\jiuyexiaozhi-app\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\work\jiuyexiaozhi-app\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\work\jiuyexiaozhi-app\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_splash" path="D:\work\jiuyexiaozhi-app\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="dialog_app_update" path="D:\work\jiuyexiaozhi-app\app\src\main\res\layout\dialog_app_update.xml" qualifiers="" type="layout"/><file name="dialog_loading" path="D:\work\jiuyexiaozhi-app\app\src\main\res\layout\dialog_loading.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="fav" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-xxxhdpi\fav.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\jiuyexiaozhi-app\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\work\jiuyexiaozhi-app\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\work\jiuyexiaozhi-app\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">安陆人社专员工作台</string></file><file path="D:\work\jiuyexiaozhi-app\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Jiuyexiaozhi" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="D:\work\jiuyexiaozhi-app\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\work\jiuyexiaozhi-app\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\work\jiuyexiaozhi-app\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\jiuyexiaozhi-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\jiuyexiaozhi-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\jiuyexiaozhi-app\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\jiuyexiaozhi-app\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>