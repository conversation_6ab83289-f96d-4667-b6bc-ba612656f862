package com.beikes.anlugrid.utils;

import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.joyu.alipayface.JYBPaaSCallback;
import com.joyu.alipayface.JYBPaaSInitCallback;
import com.joyu.alipayface.Response;
import com.joyu.alipayfacecom.AlipayFaceCommon;

import org.json.JSONException;
import org.json.JSONObject;

public class AlipayManager {
    private static final String TAG = "AlipayManager";

    AlipayFaceCommon common;
    private boolean isInit = false;
    private boolean isStartBPaaSService = false;
    private String initError = "";
    private FaceCall call;

    public AlipayManager(Context context) {
        common = AlipayFaceCommon.getInstance(context);
    }

    public void init() {
        Log.d(TAG, "init: --------");
        common.init(null, new JYBPaaSInitCallback.Stub() {
            @Override
            public void onSuccess() {
                showToast("init.初始化成功");
                isInit = true;
                startService();
            }

            @Override
            public void onFail(String error) {
                showToast("init.初始化失败：" + error);
                isInit = false;
                initError = error;
            }
        });
    }

    public boolean invokeScanFace() {
        Log.d(TAG, "startScanFace");
        common.startScanFace(new JYBPaaSCallback.Stub() {
            @Override
            public void onResponse(Response response) throws RemoteException {
                showToast("startScanFace:" + response.toString());
            }

            @Override
            public void onEvent(String event, String content, Bundle var3) throws RemoteException {
                showToast("startScanFace.事件：" + event + ",内容：" + content);
            }
        });
        return isStartBPaaSService;
    }

    public void release() {
        common.release();
    }

    private void startService() {
        Log.d(TAG, "startService");
        Bundle params = new Bundle();
        params.putBoolean("need1v1", false);
        String appid = "2021004143621535";
        params.putString("merchantAppId", appid);//商户ID 捷宇测试
        common.startBPaaSService(params, new JYBPaaSCallback.Stub() {
            @Override
            public void onResponse(Response response) throws RemoteException {
                showToast("startService:" + response.toString());
            }

            @Override
            public void onEvent(String event, String content, Bundle var3) throws RemoteException {
                showToast("startService.事件：" + event + ",内容：" + content);
                if (!TextUtils.isEmpty(content) && content.startsWith("{") && content.endsWith("}")) {
                    if (content.contains("StartVerifyIDResult") && content.contains("StartBPaaSService success")) {
                        isStartBPaaSService = true;
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(content);
                        if (!jsonObject.has("code")) {
                            return;
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    invokeResume();
                    if (call != null) {
                        call.onInfo(content);
                    }
                } else {
                    showToast("事件：" + event + ",内容：" + content);
                }
            }
        });

    }

    private void invokeResume() {
        common.resumeScanFace(new JYBPaaSCallback.Stub() {
            @Override
            public void onResponse(Response response) throws RemoteException {
                showToast(response.toString());
            }

            @Override
            public void onEvent(String event, String content, Bundle var3) throws RemoteException {

            }
        });
    }

    private void showToast(String msg) {
        Log.d(TAG, msg);
    }

    public boolean isInit() {
        return isInit;
    }

    public String getInitError() {
        return initError;
    }

    public void setCall(FaceCall call) {
        this.call = call;
    }

    public interface FaceCall {
        void onInfo(@NonNull String str);
    }
}
