package com.beikes.anlugrid.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.beikes.anlugrid.activity.SplashActivity

/**
 * @Package: com.bkty.recruitmachine.receiver
 * @CreateDate: 2022/2/22 16:22
 * @Author: zphaonu
 * @Contact: <EMAIL>
 * @Description: 自启动广播
 */
class BootReceiver : BroadcastReceiver() {

    override fun onReceive(p0: Context, p1: Intent?) {
        when (p1?.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                val intent = Intent(p0, SplashActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                p0.startActivity(intent)
            }
        }
    }
}