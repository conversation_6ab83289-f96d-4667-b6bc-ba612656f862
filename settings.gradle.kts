pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven("https://maven.aliyun.com/repository/public") // 阿里云公共仓库
        maven("https://maven.aliyun.com/repository/google") // 阿里云 Google 仓库
        maven("https://maven.aliyun.com/repository/jcenter") // 阿里云 JCenter 仓库
        maven("https://jitpack.io")
    }
}

rootProject.name = "jiuyexiaozhi"
include(":app")
 