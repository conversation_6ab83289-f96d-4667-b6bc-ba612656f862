{"logs": [{"outputFile": "com.beikes.anlugrid.test.app-mergeDebugAndroidTestResources-34:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,404,501,609,720,2148", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "199,301,399,496,604,715,837,2244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f8d3fea1113dec55401802f80912d08a\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1023,1114,1187,1262,1338,1411,1488,1554", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,74,75,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1018,1109,1182,1257,1333,1406,1483,1549,1670"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1020,1119,1221,1317,1398,1491,1583,1673,1760,1851,1924,1999,2075,2249,2326,2392", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,74,75,72,76,65,120", "endOffsets": "932,1015,1114,1216,1312,1393,1486,1578,1668,1755,1846,1919,1994,2070,2143,2321,2387,2508"}}]}]}