<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:id="@+id/flWeb"
    android:background="@color/white"
    tools:context=".activity.MainActivity">


    <com.tencent.smtt.sdk.WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:layout_gravity="center"
        android:orientation="vertical"
        android:gravity="center"
        android:id="@+id/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="加载中..."
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


    </LinearLayout>


</FrameLayout>