# 默认可以使用Android Studio 2024最新版本进行开发和编译，打包。

# 手动在根目录下创建 local.properties 文件

填入

sdk.dir=C\:\\Users\\zhusi\\AppData\\Local\\Android\\Sdk

路径需要调整为你本地Android SDK路径

# 此APP默认是给就业小知使用的，如果需要将其给其他项目使用，有以下几处需要修改
此分支给 安陆人社专员工作台 使用

## 默认命名空间

默认是com.beikes.jiuyexiaozhi

其他APP需要使用其他命名空间，因为有可能存在APP A唤起APP B的功能，而且同一台机器对于同一个包名安装也会有问题

需要搜索整体代码，将命名空间和文件夹一起改名

## 图标

路径为 app\src\main\res\mipmap-xxxhdpi\fav.png

## APP名称

路径为 app\src\main\res\values\strings.xml

## 打包版本号

修改 app\build.gradle.ktsapp\build.gradle.kts 中的versionCode和versionName

versionName需要和运营平台绑定的软件包管理类别对应版本号

## 打包名称

app\build.gradle.kts 中 variantOutput.outputFileName

打包时请使用根目录 jiuyexiaozhi.kts 签名文件，密码为 **jiuyexiaozhi**

如何打包：点击顶部菜单-Build-Generate Signed App Bundle or Apk-选择APK-选择签名文件，填入密码，KeyAlias填key0-开始打包-APK文件在app/release目录下。默认以时间戳命名。

## 默认Webview启动路径

app\src\main\java\com\beikes\anlugrid\utils\GlobalSPUtils.kt

中的 fun getUrl() 返回值

可在app\src\main\java\com\beikes\anlugrid\config.kt中修改

## 自动更新检查

app\src\main\java\com\beikes\anlugrid\base\Updator.kt 中的payload4App参数

可在app\src\main\java\com\beikes\anlugrid\config.kt中修改

## JSBridge新增方法

app\src\main\java\com\beikes\anlugrid\activity\MainActivity.kt

中的JSBridge类需要新增函数定义来与内部Webview进行交互，已有的一些方法也可通过此类查看

webview 中唤起其他app，使用 openApp(packageName) 方法进行调用

## 限制竖屏/横屏

路径：app/src/main/AndroidManifest.xml
搜索其中：android:screenOrientation  可修改值 portrait：强制纵向模式   landscape：强制横向模式（默认为逆时针旋转90度，即底部向左）。  user 跟随用户旋转

## 全屏配置|非全屏配置
路径 app/src/main/res/values/themes.xml  主要为细节样式调整
```xml
<style name="Theme.Jiuyexiaozhi" parent="android:Theme.Material.Light.NoActionBar">
    <!-- 非全屏，显示状态栏 -->
    <item name="android:windowFullscreen">false</item>
    <!-- 让状态栏字体颜色变成黑色（API 23+支持） -->
    <item name="android:windowLightStatusBar">true</item>
    <!-- 让布局自动适配状态栏，内容不会顶到状态栏里 -->
    <item name="android:fitsSystemWindows">true</item>
    <!-- 设置状态栏颜色为白色 -->
    <item name="android:statusBarColor">@android:color/white</item>
</style>
```
路径 app/src/main/java/com/beikes/anlugrid/config.kt
中 defaultFullScreen 属性 true 全屏  false 非全屏 