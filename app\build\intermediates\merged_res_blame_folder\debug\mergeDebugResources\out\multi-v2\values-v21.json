{"logs": [{"outputFile": "com.beikes.anlugrid.app-mergeDebugResources-54:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3604e631eb8339ca47ab80b99f81649b\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,3,4,5,277,278,279,280,281,284", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,19474,19590,19716,19842,19970,20142", "endLines": "2,3,4,5,277,278,279,280,283,288", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,19585,19711,19837,19965,20137,20489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\459484f9ece9bcfb108010f0bb082e93\\transformed\\utilcodex-1.31.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "14", "endColumns": "12", "endOffsets": "869"}, "to": {"startLines": "6", "startColumns": "4", "startOffsets": "368", "endLines": "18", "endColumns": "12", "endOffsets": "1182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd8e450179c94a5076653a8c92d3a411\\transformed\\appcompat-1.2.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8499,8684,11670,11867,12066,12189,12312,12425,12608,12863,13064,13153,13264,13497,13598,13693,13816,13945,14062,14239,14338,14473,14616,14751,14870,15071,15190,15283,15394,15450,15557,15752,15863,15996,16091,16182,16273,16366,16483,16622,16693,16776,17456,17513,17571,18265", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8494,8679,11665,11862,12061,12184,12307,12420,12603,12858,13059,13148,13259,13492,13593,13688,13811,13940,14057,14234,14333,14468,14611,14746,14865,15066,15185,15278,15389,15445,15552,15747,15858,15991,16086,16177,16268,16361,16478,16617,16688,16771,17451,17508,17566,18260,18966"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,34,36,37,38,39,41,43,44,45,46,47,49,51,53,55,57,59,60,65,67,69,70,71,73,75,76,77,78,79,80,123,126,169,172,175,177,179,181,184,188,191,192,193,196,197,198,199,200,201,204,205,207,209,211,213,217,219,220,221,222,224,228,230,232,233,234,235,236,237,239,240,241,251,252,253,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1187,1278,1381,1484,1589,1696,1805,1914,2023,2132,2241,2348,2451,2570,2725,2880,2985,3106,3207,3354,3495,3598,3717,3824,3927,4082,4253,4402,4567,4724,4875,4994,5345,5494,5643,5755,5902,6055,6202,6277,6366,6453,6554,6657,9415,9600,12370,12567,12766,12889,13012,13125,13308,13563,13764,13853,13964,14197,14298,14393,14516,14645,14762,14939,15038,15173,15316,15451,15570,15771,15890,15983,16094,16150,16257,16452,16563,16696,16791,16882,16973,17066,17183,17322,17393,17476,18099,18156,18214,18838", "endLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,37,38,40,42,43,44,45,46,48,50,52,54,56,58,59,64,66,68,69,70,72,74,75,76,77,78,79,122,125,168,171,174,176,178,180,183,187,190,191,192,195,196,197,198,199,200,203,204,206,208,210,212,216,218,219,220,221,223,227,229,231,232,233,234,235,236,238,239,240,250,251,252,264,276", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1273,1376,1479,1584,1691,1800,1909,2018,2127,2236,2343,2446,2565,2720,2875,2980,3101,3202,3349,3490,3593,3712,3819,3922,4077,4248,4397,4562,4719,4870,4989,5340,5489,5638,5750,5897,6050,6197,6272,6361,6448,6549,6652,9410,9595,12365,12562,12761,12884,13007,13120,13303,13558,13759,13848,13959,14192,14293,14388,14511,14640,14757,14934,15033,15168,15311,15446,15565,15766,15885,15978,16089,16145,16252,16447,16558,16691,16786,16877,16968,17061,17178,17317,17388,17471,18094,18151,18209,18833,19469"}}]}]}