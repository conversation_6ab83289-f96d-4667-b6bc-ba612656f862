  
targetContext android.app.Instrumentation  packageName android.content.Context  
AndroidJUnit4 androidx.test.ext.junit.runners  InstrumentationRegistry androidx.test.platform.app  getInstrumentation 2androidx.test.platform.app.InstrumentationRegistry  
AndroidJUnit4 com.beikes.anlugrid  ExampleInstrumentedTest com.beikes.anlugrid  InstrumentationRegistry com.beikes.anlugrid  RunWith com.beikes.anlugrid  Test com.beikes.anlugrid  assertEquals com.beikes.anlugrid  InstrumentationRegistry +com.beikes.anlugrid.ExampleInstrumentedTest  assertEquals +com.beikes.anlugrid.ExampleInstrumentedTest  
AndroidJUnit4 	org.junit  InstrumentationRegistry 	org.junit  RunWith 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         