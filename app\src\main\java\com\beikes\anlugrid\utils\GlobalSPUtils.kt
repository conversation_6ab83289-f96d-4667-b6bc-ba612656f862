package com.beikes.anlugrid.utils

import com.beikes.anlugrid.defaultWebviewUrl
import android.annotation.SuppressLint
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.SPUtils


/**
 * @Package: com.beike.smartterminal.utils
 * @CreateDate: 2024/8/16 15:09
 * @Author: zphaonu
 * @Contact: <EMAIL>
 * @Description:
 */
object GlobalSPUtils {

    private const val spName = "global"

    private fun getGlobalSP() = SPUtils.getInstance(spName)

    private const val KEY_URL = "url"
    private const val KEY_DEVICE_ID = "deviceId"


    fun getUrl(): String = getGlobalSP().getString(KEY_URL, defaultWebviewUrl)

    fun saveUrl(url: String) = getGlobalSP().put(KEY_URL, url)

    fun getDeviceId(): String = getGlobalSP().getString(KEY_DEVICE_ID, getDeviceIdOrSN())

    private fun saveDeviceId(deviceId: String) = getGlobalSP().put(KEY_DEVICE_ID, deviceId)


    @SuppressLint("PrivateApi", "HardwareIds")
    private fun getAdbSerialNumber(): String? {
        try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getMethod("get", String::class.java)
            var serial = method.invoke(null, "ro.serialno") as String?
            if (serial == null || serial.isEmpty()) {
                serial = method.invoke(null, "ro.boot.serialno") as String?
            }
            return serial
        } catch (e: java.lang.Exception) {
            return null
        }
    }

    /**
     * 获取设备id或sn码
     */
    private fun getDeviceIdOrSN(): String {
        var deviceId = ""
        deviceId = try {
            getAdbSerialNumber().let { s ->
                if (!s.isNullOrEmpty() && (s.startsWith('K') || s.startsWith('X') || s.startsWith('T'))) {
                    s
                } else {
                    DeviceUtils.getUniqueDeviceId()
                }
            }
        } catch (e: Exception) {
            ""
        }
        if (deviceId.isNotEmpty()) {
            saveDeviceId(deviceId)
        }
        return deviceId
    }

}